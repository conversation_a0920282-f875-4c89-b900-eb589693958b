import { createClient } from '@clickhouse/client';
import moment from 'moment';
import { safeParseJson } from './utils';

const isDevelopment = process.env.NEXT_PUBLIC_DEV_STAGE === 'dev';

export const clickhouse = createClient({
  url: process.env.CLICKHOUSE_URL || 'http://localhost:8123',
  username: process.env.CLICKHOUSE_USERNAME || 'default',
  password: process.env.CLICKHOUSE_PASSWORD || '',
  database:
    (isDevelopment
      ? process.env.CLICKHOUSE_DATABASE_DEV
      : process.env.CLICKHOUSE_DATABASE_PROD) || 'default'
});

export const attemptConnection = async () => {
  const rows = await clickhouse.query({
    query: 'SELECT 1',
    format: 'JSONEachRow'
  });

  console.log('Result: ', await rows.json());
};

const parseQueryParams = (query_params: {
  [key: string]: unknown;
}): { [key: string]: unknown } => {
  const parsed = {};
  for (const key in query_params) {
    if (query_params[key] === undefined) {
      continue;
    } else if (typeof query_params[key] === 'object') {
      const object = query_params[key];
      if (object instanceof Date) {
        parsed[key] = moment(object).utc().format('YYYY-MM-DD HH:mm:ss');
      } else {
        parsed[key] = query_params[key];
      }

      continue;
    }

    parsed[key] = query_params[key];
  }

  return parsed;
};

export const getFirst = async (
  query: string,
  query_params: { [key: string]: unknown }
): Promise<any> => {
  const rows = await clickhouse.query({
    query,
    query_params: parseQueryParams(query_params),
    format: 'JSONEachRow'
  });

  return (await rows.json())[0] as any;
};

// Due to Clickhouse bug, modifying column type to JSON is not possible (at 21 Jul 2024)

const forcedJsonColumns = ['properties'];

export const getAll = async (
  query: string,
  query_params: { [key: string]: unknown }
): Promise<any[]> => {
  const rows = await clickhouse.query({
    query,
    query_params: parseQueryParams(query_params),
    format: 'JSONEachRow'
  });

  const result = await rows.json();

  for (const row of result) {
    for (const column of forcedJsonColumns) {
      if (row[column]) {
        row[column] = safeParseJson(row[column]);
      }
    }
  }

  return result;
};

export const create = async (tableName: string, data: any) => {
  await clickhouse.insert({
    table: tableName,
    values: [data],
    format: 'JSONEachRow',
  });
};

export const query = async (query: string, params: any) => {
  const rows = await clickhouse.query({
    query,
    format: 'JSONEachRow',
    query_params: parseQueryParams(params)
  });

  return await rows.json();
}

export const command = async (query: string, params) => {
  return await clickhouse.command({
    query,
    query_params: parseQueryParams(params)
  });
}