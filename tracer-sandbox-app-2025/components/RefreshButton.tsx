import { useInvitesContext } from '@/context/InvitesContext';
import { useServicesContext } from '@/context/ServicesContext/ServicesContext';
import { useTeamsContext } from '@/context/TeamsContext';
import { faSyncAlt } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import cn from 'clsx';
import { useState } from 'react';

interface RefreshButtonProps {
  className?: string;
}

export function RefreshButton({ className }: RefreshButtonProps) {
  const { forceRefresh: refreshInvites } = useInvitesContext();
  const { forceRefresh: refreshTeams } = useTeamsContext();
  const { forceRefresh: refreshServices } = useServicesContext();

  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = () => {
    const refreshStartTime = Date.now();
    setIsRefreshing(true);
    refreshInvites();
    refreshTeams();
    refreshServices();
    const refreshEndTime = Date.now();
    setTimeout(() => {
      setIsRefreshing(false);
    }, Math.max(0, 3000 - (refreshEndTime - refreshStartTime)));
  };


  return (
    <button onClick={handleRefresh}>
      <FontAwesomeIcon
        icon={faSyncAlt}
        className={cn(
          `cursor-pointer text-md hover:scale-110 ${
            isRefreshing
              ? 'animate-spin opacity-100'
              : ''
          }`,
          className
        )}
      />
    </button>
  );
}
