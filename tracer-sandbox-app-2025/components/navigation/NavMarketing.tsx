import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { UserButton } from '@clerk/nextjs';

export const MarketingNavbar = () => {
  return (
    <nav className="bg-black border-b border-gray-800">
      <div className="w-full px-8 grid grid-cols-2 items-center h-16">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/assets/tracer_logo_body_black_bg_transparent.png"
            alt="Tracer Logo"
            width={120}
            height={75}
            className="rounded-sm"
            style={{ width: 'auto', height: 'auto' }}
          />
          <span 
            className="py-[3px] px-3 rounded-md bg-[#4EC6B0] text-white text-[14px] font-bold"
            style={{ transform: 'translateY(2px)' }}
          >
            Beta
          </span>
        </Link>
        <div className="justify-self-end">
          <UserButton afterSignOutUrl="/" />
        </div>
      </div>
    </nav>
  );
};
