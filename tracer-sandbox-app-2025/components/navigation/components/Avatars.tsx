import { Avatar } from '@chakra-ui/react';
import cn from 'clsx';
import { TeamMember } from 'src/types';

interface AvatarImgProps {
  name?: string;
  users: TeamMember[];
  className?: string;
  onClick?: () => void;
}

export const Avatars = ({
  users,
  className
}: AvatarImgProps) => {
  return (
    <div className="flex items-center">
      {users.filter(user => user.state !== 'invited').map((user) => (
        <Avatar
          key={user.userName}
          name={user.userName}
          src={user.userAvatarUrl}
          className={cn('h-[35px] w-[35px] mr-1', className)}
        />
      ))}
      <div
        className={cn(
          'h-[35px] w-[35px] rounded-full mr-1 bg-gradient-to-bl from-blue-900 to-indigo-600 flex items-center justify-center'
        )}
      >
        <a className="text-[12px] font-bold">＋</a>
      </div>
    </div>
  );
};
