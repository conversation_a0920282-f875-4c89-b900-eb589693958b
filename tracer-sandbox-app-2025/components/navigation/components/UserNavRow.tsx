import { useServicesContext } from '@/context/ServicesContext/ServicesContext';
import { useTeamsContext } from '@/context/TeamsContext/index';
import { UserButton, useUser as useClerkUser } from '@clerk/nextjs';
import { useRouter } from 'next/router';
import { Avatars } from './Avatars';
import { BreadCrumbs } from './BreadCrumbs';
import { TeamSelector } from './TeamSelector';
import Link from 'next/link';
import { useEffect } from 'react';

export function UserNavRow({ className }: { className?: string }) {
  const { user: clerkUser } = useClerkUser();
  const { activeService, activeRun } = useServicesContext();
  const { otherUsers } = useTeamsContext();

  const router = useRouter();

  const handleClick = (path: string) => () => router.push(path);

  const username =
    clerkUser?.firstName ||
    clerkUser?.username ||
    clerkUser?.primaryEmailAddress ||
    clerkUser?.id ||
    'undefined_user';

  const formattedUsername =
    username === 'vincenthus' || username === 'davincios'
      ? 'vincent'
      : username;

  const isDemo = router.pathname.includes('demo');

  useEffect(() => {
    // This effect will run whenever activeService changes
    // Additional logic can be placed here if needed
  }, [activeService]);

  return (
    <div className={`relative flex items-center ${className}`}>
      <header>
        <UserButton afterSignOutUrl="/" />
      </header>

      <BreadCrumbs
        handleClickOnUsername={handleClick('/app/pipelines')}
        handleClickOnPipelineName={handleClick(
          `/app/runs/${activeService}/runs`
        )}
        username={`${formattedUsername}`}
        activeService={activeService}
        activeRun={activeRun}
        className="ml-3"
        isDemo={isDemo}
      />
      <Link className="font-bold md:font-normal text-[15px] mr-3" href="/">
        Tracer beta v0.2
      </Link>
      <TeamSelector />
      <Avatars
        users={otherUsers}
        className="h-[35px] w-[35px]"
        onClick={handleClick('/')}
      />
    </div>
  );
}
