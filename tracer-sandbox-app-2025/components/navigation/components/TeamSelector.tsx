import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { useTeamsContext } from '@/context/TeamsContext/index';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import cn from 'clsx';

export const TeamSelector = () => {
  const { availableTeams, selectedTeam, changeTeamAndNavigate } =
    useTeamsContext();

  if (!selectedTeam) {
    return null;
  }

  return (
    <div className="mr-3">
      <DropdownMenu>
        <DropdownMenuTrigger className="border border-white/60 rounded-[3px] h-[30px] text-[12px] p-1 flex justify-center items-center">
          <span className="ml-3">{selectedTeam?.name || 'Unknown team'}</span>
          <FontAwesomeIcon icon={faChevronDown} className="ml-3" />
        </DropdownMenuTrigger>
        <DropdownMenuContent className={cn(`${300} z-[4500]`)}>
          {availableTeams.map((team) => (
            <DropdownMenuItem
              className="text-center hover:bg-white/80 hover:text-black/90 cursor-pointer"
              key={team.name}
              onSelect={() => changeTeamAndNavigate(team.id, '/app/pipelines')}
            >
              {team.name}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
