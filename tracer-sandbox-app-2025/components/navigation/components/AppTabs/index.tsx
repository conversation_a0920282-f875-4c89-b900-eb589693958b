import { useServicesContext } from '@/context/ServicesContext/ServicesContext';
import { useTeamsContext } from '@/context/TeamsContext';
import { Tab, TabList, Tabs } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';

const getMainLinks = ({ teamId, serviceName }) => [
  {
    path: `runs/${serviceName}/flowsheet?teamId=${teamId}`,
    name: 'Flowsheet',
    id: 'flowsheet'
  },
  {
    path: `runs/${serviceName}/runs?teamId=${teamId}`,
    name: 'Runs',
    id: 'runs'
  },
  {
    path: `runs/${serviceName}/logs?teamId=${teamId}`,
    name: 'Logs',
    id: 'logs'
  },
  {
    path: `runs/${serviceName}/metrics?teamId=${teamId}`,
    name: 'Metrics',
    id: 'metrics'
  },
  {
    path: `runs/${serviceName}/errors?teamId=${teamId}`,
    name: 'Errors',
    id: 'errors'
  }
];

const getPipelineSituationLinks = ({ teamId }) => [
  { path: `pipelines?teamId=${teamId}`, name: 'Pipelines', id: 'pipelines' },
  { path: `setup?teamId=${teamId}`, name: 'Setup', id: 'setup' },
  { path: `all-errors?teamId=${teamId}`, name: 'Errors', id: 'all-errors' },
  {
    path: `billing?teamId=${teamId}`,
    name: 'Billing',
    id: 'billing',
    hidden: true
  },
  { path: `teams?teamId=${teamId}`, name: 'Teams', id: 'teams' }
];

export function AppTabs() {
  const router = useRouter();
  const [tabIndex, setTabIndex] = useState(0);
  const { activeService } = useServicesContext();
  const { teamId, selectedTeam, changeTeam } = useTeamsContext();

  useEffect(() => {
    if (!teamId) {
      const teamId = new URLSearchParams(window.location.search).get('teamId');
      changeTeam(teamId);
    }
  }, [teamId]);

  const links = useMemo(() => {
    const isPipeline =
      router.pathname.includes('pipelines') ||
      router.pathname.includes('setup') ||
      router.pathname.includes('teams') ||
      router.pathname.includes('all-errors');
    return isPipeline
      ? getPipelineSituationLinks({ teamId }).filter((link) => !link.hidden)
      : getMainLinks({
          teamId,
          serviceName: activeService
        });
  }, [router.pathname, activeService, selectedTeam]);

  useEffect(() => {
    const findTabIndex = () =>
      links.findIndex(({ id }) => router.pathname.endsWith(id));
    const newTabIndex = findTabIndex();
    if (newTabIndex !== -1 && newTabIndex !== tabIndex) {
      setTabIndex(newTabIndex);
    }
  }, [router.pathname, links, tabIndex]);

  const handleTabsChange = (index) => {
    const newPath = `/app/${links[index].path}`;
    if (router.pathname !== newPath) {
      router.replace(newPath);
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <Tabs index={tabIndex} onChange={handleTabsChange}>
        <TabList className="border-none sm:-ml-6">
          {links.map(({ path, name }) => (
            <Tab
              key={path}
              className="text-[14px] pb-[8px] px-[24px] font-medium bg-transparent"
              _selected={{
                borderBottom: 'solid 6px #004CDC',
                fontWeight: '600',
                backgroundColor: 'transparent'
              }}
            >
              {name}
            </Tab>
          ))}
        </TabList>
      </Tabs>
    </div>
  );
}
