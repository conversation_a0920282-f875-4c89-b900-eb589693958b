import { faCopy } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useRouter } from 'next/router';
import { RefreshButton } from '../../RefreshButton';

const DashIcon = () => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="block shrink-0"
  >
    <path
      d="M9.75 20.25L14.25 3.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
    />
  </svg>
);

interface BreadCrumbsProps {
  handleClickOnUsername: () => void;
  handleClickOnPipelineName: () => void;
  username: string;
  activeRun: string;
  activeService: string;
  className?: string;
  isDemo: boolean;
}

export function BreadCrumbs({
  handleClickOnUsername,
  handleClickOnPipelineName,
  username,
  className,
  isDemo,
  activeRun,
  activeService
}: BreadCrumbsProps) {
  const router = useRouter();
  const copyToClipboard = async () => {
    if (activeRun) {
      await navigator.clipboard.writeText(activeRun);
    }
  };

  const isRunsPage =
    router.pathname.includes('runs') ||
    router.pathname.includes('app/pipelines/');

  return (
    <div className={`flex flex-grow items-center ${className}`}>
      <div
        onClick={handleClickOnUsername}
        className="hidden items-center rounded p-1 text-[15px] max-md:text-md font-medium leading-3 transition sm:flex cursor-pointer"
      >
        {username}'s&nbsp;pipelines
      </div>
      {isRunsPage && activeService && (
        <>
          <DashIcon />
          <div className="hidden sm:block"></div>
          <div className="hidden items-center p-1 leading-3 sm:flex cursor-pointer text-[15px]">
            <div
              className="flex items-center rounded max-md:text-md font-medium leading-none transition"
              onClick={handleClickOnPipelineName}
            >
              {activeService}
            </div>
          </div>
        </>
      )}
      {isRunsPage && activeRun && (
        <>
          <DashIcon />
          <div className="hidden sm:block"></div>
          <div className="hidden items-center p-1 leading-3 sm:flex cursor-pointe text-[15px]">
            <div
              className="flex items-center rounded text-[15px] font-medium leading-none transition"
              onClick={handleClickOnPipelineName}
            >
              {activeRun}
            </div>
            <FontAwesomeIcon
              icon={faCopy}
              className="mx-2 cursor-pointer opacity-80 text-[15px] hover:opacity-100 hover:scale-110 transform transition-all duration-150"
              onClick={copyToClipboard}
            />
          </div>
        </>
      )}
      {!activeService && !isDemo && (
        <RefreshButton />
      )}
    </div>
  );
}
