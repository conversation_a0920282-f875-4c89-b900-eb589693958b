import cn from 'clsx';
import { AppTabs } from './components/AppTabs';
import { UserNavRow } from './components/UserNavRow';

export function AppNavbar() {
  const baseClasses =
    'transition-all duration-50 z-[4000] pl-[56px] pr-[56px] border-b border-[#c1c9d0]/[.3] grid auto-rows-auto sticky top-0';
  const themeClasses = 'bg-black';

  const theme = cn(baseClasses, themeClasses, 'pt-[14px]');

  return (
    <div className={theme}>
      <UserNavRow />
      <AppTabs />
    </div>
  );
}
