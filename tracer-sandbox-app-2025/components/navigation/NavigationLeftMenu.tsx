import { useTeamsContext } from '@/context/TeamsContext';
import { 
  // Bug, Cog, Home, Play, Users, 
  LineChart } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const NAV_ITEMS = [
  {
    iconImg: '/assets/tracer-logo-trimmed-sq.png',
    path: '/'
  },
  // {
  //   icon: Home,
  //   path: '/app/pipelines'
  // },
  // { icon: Bug, path: '/app/all-errors' },
  // {
  //   icon: Cog,
  //   path: '/app/setup'
  // },
  // { icon: Users, path: '/app/teams' },
  {
    icon: LineChart,
    path: '/grafana'
  },
  // {
  //   icon: Play,
  //   path: 'https://gitpod.io/?autostart=true#https://github.com/TracerBio/tracer-workflow-templates',
  //   external: true
  // }
];

export function NavigationLeftMenu() {
  const router = useRouter();
  const { teamId } = useTeamsContext();

  return (
    <nav className="z-[10000] fixed bg-[#0e0e10] text-white h-screen w-[60px] p-2 px-3 text-center border-r border-white/30">
      {NAV_ITEMS.map(({ icon: Icon, iconImg, path, /*external*/ }) => {
        const isActive = router.pathname === path;
        const linkClass = `block my-2 mb-4 p-1 py-1 w-full rounded-sm transition-colors duration-200 ease-in-out
          ${isActive ? 'bg-white' : 'hover:bg-white/10'}`;

        const query = new URLSearchParams({
          teamId
        }).toString();
        const href = `${path}?${query}`;
        if (external) {
          return (
            <Link
              key={path}
              href={href}
              className={linkClass}
              // target="_blank"
              // rel="noopener noreferrer"
            >
              {!!iconImg && (
                <img src={iconImg} alt="Tracer Logo" className="mx-auto" />
              )}
              {!!Icon && (
                <Icon
                  className={`mx-auto ${
                    isActive ? 'text-black' : 'text-white'
                  }`}
                  size={16}
                />
              )}
            </Link>
          );
        }

        return (
          <Link key={path} href={href} className={linkClass}>
            {!!iconImg && <img src={iconImg} alt="Tracer Logo" />}
            {!!Icon && (
              <Icon
                className={`mx-auto ${isActive ? 'text-black' : 'text-white'}`}
                size={18}
              />
            )}
          </Link>
        );
      })}
    </nav>
  );
}

export default NavigationLeftMenu;
