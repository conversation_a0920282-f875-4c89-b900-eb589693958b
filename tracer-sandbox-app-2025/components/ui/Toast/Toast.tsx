import React, { useEffect, useRef } from 'react';
import { useUI } from '@/context/UIContext';
import { StyledToast } from './StyledToast';

export const ToastComponent = () => {
  const {
    state: { displayToast, toastTitle, toastContent, toastDuration },
    controls: { closeToast }
  } = useUI();
  const timerRef = useRef(0);

  useEffect(() => {
    if (displayToast) {
      window.clearTimeout(timerRef.current);
      timerRef.current = window.setTimeout(() => {
        closeToast();
      }, toastDuration);
    }
    return () => clearTimeout(timerRef.current);
  }, [displayToast, closeToast, toastDuration]);

  return displayToast ? (
    <StyledToast
      toastTitle={toastTitle}
      toastContent={toastContent}
      closeToast={closeToast}
    />
  ) : null;
};
