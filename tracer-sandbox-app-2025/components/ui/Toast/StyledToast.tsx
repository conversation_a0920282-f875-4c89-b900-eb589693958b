import { <PERSON> } from 'lucide-react';
import { ShadButton } from '@/components/ui/ShadButton';

export const StyledToast = ({ toastContent, closeToast, toastTitle }) => (
  <div className="fixed bottom-6 right-6 mb-6 max-w-xs w-full bg-black text-gray-100 rounded-sm shadow-lg p-1 border-solid border-[2px] border-white/50 animate-pulseCustom">
    <div className="p-3 rounded-sm ">
      <div className="flex items-center space-x-2">
        <Hammer className="text-white" size={24} />
        <h2 id={toastTitle} className="text-lg font-semibold text-white">
          Under construction
        </h2>
      </div>
      <p className="text-sm mt-2 text-gray-300">{toastContent}</p>

      <ShadButton
        size="sm"
        className="mt-4 hover:scale-105 transition-transform duration-150"
        onClick={closeToast}
      >
        Ok!
      </ShadButton>
    </div>
  </div>
);
