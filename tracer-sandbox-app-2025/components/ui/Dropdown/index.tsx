import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

import './styles.scss';

interface DropdownProps {
  options: { value: string; name: string; marked?: boolean }[];
  selected: string;
  setSelected: (value: string) => void;
  prefix?: string;
  markComponent?: JSX.Element;
}

const Dropdown = (props: DropdownProps) => {
  const [active, setActive] = useState(false);

  return (
    <div className="dropdown-container">
      <div
        className={`dropdown-button ${active ? 'active' : ''}`}
        onClick={() => setActive((a) => !a)}
      >
        <span>
          {props.prefix ? `${props.prefix} ` : ''}
          <strong>
            {props.options.find((s) => s.value === props.selected)?.name}
          </strong>
          {props.options.find((o) => o.value === props.selected && o.marked) &&
          props.markComponent
            ? props.markComponent
            : null}
        </span>
        <ChevronDown
          width={16}
          height={16}
          className={`dropdown-chevron ${active ? 'active' : ''}`}
        />
      </div>
      {active && (
        <div className="dropdown">
          {props.options.map((option) => (
            <button
              key={option.value}
              onClick={() => {
                props.setSelected(option.value);
                setActive(false);
              }}
            >
              {option.name}
              {option.marked && props.markComponent
                ? props.markComponent
                : null}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dropdown;
