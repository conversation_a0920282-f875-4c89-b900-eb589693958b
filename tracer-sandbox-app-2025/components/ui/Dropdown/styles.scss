      .dropdown-container {
        display: flex;
        flex-direction: row;
        // margin-left: 1rem;
        position: relative;
        letter-spacing: 1px;

        @keyframes dropdown-show {
          from {
            opacity: 0;
            transform: translateY(-0.5rem);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .dropdown {
          position: absolute;
          left: 0;
          top: 2rem;
          display: flex;
          flex-direction: column;
          width: 100%;
          animation: dropdown-show 0.5s ease;
          border: 1px solid #666666;
          border-bottom: none;
          z-index: 1;
          max-height: 50vh;
          overflow-y: scroll;

          button {
            padding: 0.25rem 0.5rem;
            border-bottom: 1px solid #666666;
            font-size: 12px;
            background-color: black;

            &:hover {
              background-color: #333333;
            }
          }
        }

        .dropdown-button {
          display: flex;
          flex-direction: row;
          align-items: center;
          cursor: pointer;
          transition: 0.1s ease transform;
          user-select: none;
          transform: translateY(-1px);
          padding: 0 1rem;

          span {
            font-size: 12px;
          }

          &:active {
            transform: translateY(1px);
          }

          .dropdown-chevron {
            transform: translateY(0.1rem);
            margin-left: 0.25rem;
            transition: 0.1s ease all;

            &.active {
              transform: translateY(0.1rem) rotate(180deg);
            }
          }
        }
      }