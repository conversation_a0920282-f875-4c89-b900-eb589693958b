import Loading from '@/components/Loading';
import { Dialog, Transition } from '@headlessui/react';
import { ArrowRightIcon } from '@heroicons/react/outline';
import { GetTierButton } from 'pages/pricing';
import { useState, useRef, Fragment } from 'react';

function ModalContent({ closeModal, setIsMsgOpen }) {
  const [isWarningVisible, setIsWarningVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formValues, setFormValues] = useState(
    process.env.NEXT_PUBLIC_NEXT_PUBLIC_DEV_STAGE === 'dev'
      ? {
          firstName: 'Judy',
          lastName: 'Alvarez',
          email: '<EMAIL>',
          companyName: 'Biotechnica'
        }
      : {
          firstName: '',
          lastName: '',
          email: '',
          companyName: ''
        }
  );

  const handleSubmit = async () => {
    console.log('Submitting the form, and performing the API request.');
    console.log(JSON.stringify(formValues));
    const { firstName, lastName, email, companyName } = formValues;
    const isValid =
      firstName !== '' &&
      lastName !== '' &&
      email.includes('@') &&
      companyName !== '';

    if (!isValid) {
      console.warn('the form has not been filled in correctly.');
      setIsWarningVisible(true);
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/addUserCMS', {
        method: 'POST',
        body: JSON.stringify({
          formValues,
          source: `Navbar Signup Modal`,
          environment: process.env.NEXT_PUBLIC_NEXT_PUBLIC_DEV_STAGE
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });
      await response.json();

      setTimeout(() => {
        setIsLoading(false);
        setIsMsgOpen(true);
        closeModal();
      }, 1400);
    } catch (e) {
      console.warn(e);
      setTimeout(() => {
        setIsLoading(false);
      }, 1400);
    }
  };

  return (
    <div className="p-4 text-black min-w-[340px]">
      <h1 className="text-black mb-10 text-2xl text-center bold">
        Join our Private Beta
      </h1>
      <div className="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
        <div className="col-span-2">
          <label
            htmlFor="first-name"
            className="block text-sm font-medium text-black"
          >
            First name*
          </label>
          <div className="mt-1">
            <input
              required
              type="text"
              name="firstName"
              id="firstName"
              autoComplete="given-name"
              className="py-3 px-4 block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 border-gray-300 rounded-xs"
              value={formValues.firstName}
              onChange={(e) =>
                setFormValues({
                  ...formValues,
                  firstName: e.target.value
                })
              }
            />
          </div>
        </div>
        <div>
          <label
            htmlFor="last-name"
            className="block text-sm font-medium text-gray-700"
          >
            Last name*
          </label>
          <div className="mt-1">
            <input
              type="text"
              required
              name="lastName"
              id="lastName"
              autoComplete="family-name"
              className="py-3 px-4 block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 border-gray-300 rounded-xs"
              value={formValues.lastName}
              onChange={(e) =>
                setFormValues({
                  ...formValues,
                  lastName: e.target.value
                })
              }
            />
          </div>
        </div>
        <div className="col-span-2">
          <label
            htmlFor="Name"
            className="block text-sm font-medium text-gray-700"
          >
            Company*
          </label>
          <div className="mt-1">
            <input
              type="text"
              required
              name="companyName"
              id="companyName"
              autoComplete="organization"
              className="py-3 px-4 block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 border-gray-300 rounded-xs"
              value={formValues.companyName}
              onChange={(e) =>
                setFormValues({
                  ...formValues,
                  companyName: e.target.value
                })
              }
            />
          </div>
        </div>
        <div className="col-span-2">
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700"
          >
            Email*
          </label>
          <div className="mt-1">
            <input
              id="email"
              required
              name="email"
              type="email"
              autoComplete="email"
              className="py-3 px-4 block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 border-gray-300 rounded-xs"
              value={formValues.email}
              onChange={(e) =>
                setFormValues({
                  ...formValues,
                  email: e.target.value
                })
              }
            />
          </div>
        </div>
        <div className="text-red my-1 col-span-2">
          {isWarningVisible &&
            'Please provide all information fields to continue'}
        </div>

        <div className="sm:col-span-2">
          {isLoading ? (
            <div className="sm:mt-0 min-h-[36px] text-center relative">
              <Loading isLoading={isLoading} />
            </div>
          ) : (
            <button
              type="submit"
              className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-xs shadow-sm text-base font-medium text-white bg-brandBlue hover:bg-[#2860bb] focus:outline-none"
              onClick={handleSubmit}
            >
              Join Beta
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export function BetaModalNavbar() {
  // THIS SHOULD BE REFACTORED INTO CONTEXT WITH A REDUCER FUNCTION
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className="inline-flex rounded-[0px] shadow bg-brandBlue
                  min-w-fit w-min-[120px] px-3 py-[2px] text-white font-medium hover:from-teal-600 hover:to-cyan-700 focus:outline-none"
      >
        Join Beta
        <ArrowRightIcon className="h-4 m-auto ml-1" />
      </button>
      <GeneralModal
        isModalOpen={isModalOpen}
        closeModal={() => setIsModalOpen(false)}
      />
    </>
  );
}

export function BetaModalBtnPricingPage({ tier }) {
  // THIS SHOULD BE REFACTORED INTO CONTEXT WITH A REDUCER FUNCTION
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <>
      <GetTierButton tier={tier} onClick={() => setIsModalOpen(true)} />
      <GeneralModal
        isModalOpen={isModalOpen}
        closeModal={() => setIsModalOpen(false)}
      />
    </>
  );
}

export function GeneralModal({ isModalOpen, closeModal }) {
  const [, setIsMsgOpen] = useState(false);
  const cancelButtonRef = useRef(null);

  return (
    <>
      <Transition.Root show={isModalOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative"
          initialFocus={cancelButtonRef}
          onClose={closeModal}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed z-9999 inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative bg-white rounded-sm text-left overflow-hidden shadow-xl transform transition-all  mt-16 sm:my-8 sm:max-w-lg sm:w-full">
                  <ModalContent
                    closeModal={closeModal}
                    setIsMsgOpen={setIsMsgOpen}
                  />
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
      {/* <Snackbar
        open={isMsgOpen}
        autoHideDuration={5200}
        onClose={() => setIsMsgOpen(false)}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center'
        }}
      >
        <Alert
          onClose={() => setIsMsgOpen(false)}
          severity="success"
          sx={{ width: '100%' }}
          elevation={6}
        >
          Thanks for joining our private beta. We will be in touch shortly to
          introduce you to what we've been working on.
        </Alert>
      </Snackbar> */}
    </>
  );
}
