.expandable-container-item {
  padding-right: 2rem;

  .expandable-container-body {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 0 1rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.2s ease-out;

    &.expanded {
      margin: 1rem;
    }
  }
}

.expandable-container-header {
    .expandable-container-icon {
      width: 2rem;
      
      margin-right: 1rem;
    }
    .expandable-container-message {
      font-size: 1.1rem;
      user-select: none;
      margin-left: 1rem;
    }
    .expandable-container-chevron {
      margin-left: auto;
      width: 1.5rem;
      height: 1.5rem;
      fill: #fff;
      transition: all 0.2s ease;
    }

    cursor: pointer;
    padding: 4px;
    height: 3em;
    display: flex;
    flex-direction: row;
    align-items: center;
    transition: all 0.2s ease;

    &:hover {
      
      transform: translateX(10px);
    }
  }