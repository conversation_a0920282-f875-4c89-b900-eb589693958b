import React from 'react';
import moment from 'moment';

export const QueryPeriodDisplay: React.FC<{
  start: string;
  end: string;
}> = ({ start, end }) => {
  if (!start || !end) {
    return null; // or <div>No period data available</div> as a placeholder
  }

  const formattedStart = moment(start, 'x').format('MMMM Do YYYY, h:mm a');
  const formattedEnd = moment(end, 'x').format('MMMM Do YYYY, h:mm a');

  const startMoment = moment(start, 'x');
  const endMoment = moment(end, 'x');
  const duration = moment.duration(endMoment.diff(startMoment));

  const formattedDurationParts = [];
  // Add months to the display if the duration includes months
  if (duration.months() > 0) {
    formattedDurationParts.push(`${duration.months()}mo`);
  }
  if (duration.days() > 0) {
    formattedDurationParts.push(`${duration.days()}d`);
  }
  formattedDurationParts.push(`${duration.hours()}h`, `${duration.minutes()}m`);
  // Cleaning up the string in case there are initial hyphens or spaces
  const formattedDuration = formattedDurationParts
    .join(' ')
    .trim()
    .replace(/^-/, '');

  return (
    <div className="text-white/70 text-md inline-block">
      Run period: {formattedStart} — {formattedEnd}
      <span className="text-sm text-white/50 ml-2">({formattedDuration})</span>
    </div>
  );
};
