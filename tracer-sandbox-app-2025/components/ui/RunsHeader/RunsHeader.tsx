import React from 'react';
import { QueryPeriodDisplay } from './QueryPeriodDisplay';
import cn from 'clsx'; // `cn` is typically used for handling dynamic class names

// Define an interface for the component props
interface RunsHeaderProps {
  start: string;
  end: string;
  title: string;
  className?: string;
}

// Functional component using TypeScript
export const RunsHeader = ({
  start,
  end,
  title,
  className = ''
}: RunsHeaderProps) => {
  const headerClasses = cn('mt-6 pt-2', className);

  return (
    <div className={headerClasses}>
      <h1 className="text-md mb-4 inline-block mr-3">{title}</h1>
      {start && end && <QueryPeriodDisplay start={start} end={end} />}
    </div>
  );
};
