import cn from 'clsx';

export const defaultButtonClass =
  'mt-2 p-4 text-white rounded bg-brandPurple hover:bg-brandPurplehover focus:outline-none focus:ring focus:ring-violet-300';

export function CButton({ text, sx, type, className, onClick }) {
  return (
    <button
      className={cn(defaultButtonClass, className)}
      type={type || 'button'}
      style={sx}
      onClick={onClick}
    >
      {text}
    </button>
  );
}
