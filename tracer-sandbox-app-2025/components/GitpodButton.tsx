import React from 'react';

interface GitPodButtonProps {
  repo: string;
  className?: string;
}

export const GitPodButton: React.FC<GitPodButtonProps> = ({
  className = ''
}) => {
  const gitpodUrl =
    'https://gitpod.io/?autostart=true#https://github.com/TracerBio/tracer-workflow-templates';

  return (
    <a
      href={gitpodUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={`inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FFB45B] transition-colors duration-200 ${className}`}
    >
      <img
        src="https://www.svgrepo.com/show/373626/gitpod.svg"
        alt="Gitpod Logo"
        className="w-5 h-5 mr-2 -ml-1 filter brightness-0"
      />
      <span className="text-sm font-medium">Try in Gitpod</span>
    </a>
  );
};
