import { useEffect, useState } from 'react';

function useLiveDuration(startUnixDate: number, isRunning: boolean) {
  const [liveDuration, setLiveDuration] = useState(0);

  useEffect(() => {
    if (!isRunning) {
      return;
    }

    const startTimestamp = startUnixDate;
    const updateDuration = () => {
      const now = Date.now();
      const elapsed = (now - startTimestamp) / 1000;
      setLiveDuration(Math.floor(elapsed));
    };

    updateDuration(); // Update immediately
    const intervalId = setInterval(updateDuration, 1000);

    return () => clearInterval(intervalId);
  }, [startUnixDate, isRunning]);

  return liveDuration;
}

export const RunDurationCell = ({
  startUnixDate,
  durationSeconds,
  showMs
}: {
  startUnixDate: number;
  durationSeconds: number;
  showMs?: boolean;
}) => {
  const isRunning = durationSeconds < 0 || durationSeconds > 1712080085;
  const liveDuration = useLiveDuration(startUnixDate, isRunning);

  const formatDuration = (seconds: number) => {
    if (showMs && seconds <= 10) {
      // show in ms
      return `${Math.round(seconds * 1000)}ms`;
    }

    const days = Math.floor(seconds / 86400);
    seconds %= 86400;
    const hours = Math.floor(seconds / 3600);
    seconds %= 3600;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    let durationString = '';
    if (days > 0) durationString += `${days}d `;
    if (hours > 0 || days > 0) durationString += `${hours}h `;
    if (minutes > 0 || hours > 0 || days > 0) durationString += `${minutes}m `;
    durationString += `${
      seconds === remainingSeconds
        ? Math.round(remainingSeconds * 10) / 10
        : remainingSeconds.toFixed(0)
    }s`;

    return durationString.trim();
  };

  if (isRunning) {
    return (
      <span className="min-w-[200px]">{formatDuration(liveDuration)}</span>
    );
  }

  return <span>{formatDuration(durationSeconds)}</span>;
};
