import React from 'react';
import Link from 'next/link';
import { TextInputField } from './TextInputField';
import cn from 'clsx';
import { ArrowRightIcon } from '@heroicons/react/outline';
import Loading from '../Loading';

export function EmailSignupForm({ className }: { className?: string }) {
  const [data, setData] = React.useState({ email: '' });
  const [isLoading, setIsLoading] = React.useState(false);
  const [, setIsMsgOpen] = React.useState(false);

  // const msg = `🎉 Thanks for joining our private beta. We will be in touch shortly to introduce you to what we've been working on.`;

  const onSubmit = async () => {
    const { email } = data;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.warn('[EmailSignupForm|onSubmit] email is invalid');
      return;
    }

    if (email) {
      setIsLoading(true);
      try {
        const response = await fetch('/api/email-signup', {
          method: 'POST',
          body: JSON.stringify({
            email,
            source: `Hero section email form`,
            environment: process.env.NEXT_PUBLIC_NEXT_PUBLIC_DEV_STAGE
          }),
          headers: {
            'Content-Type': 'application/json'
          }
        });
        const resJson = await response.json();

        console.log('[EmailSignupForm] resJson', resJson);

        setTimeout(() => {
          setIsLoading(false);
          setIsMsgOpen(true);
        }, 1400);
      } catch (e) {
        console.warn('[EmailSignupForm|onSubmit] an error occured', e);
        setTimeout(() => {
          setIsLoading(false);
        }, 1400);
      }
    } else {
      console.warn('[EmailSignupForm|onSubmit] there is no email present.');
    }
  };

  return (
    <form action="#" className={cn(className)}>
      <div className="sm:flex">
        <div className="min-w-0 flex-1">
          <label htmlFor="email" className="sr-only">
            Email address
          </label>
          <TextInputField
            onChange={(e) => setData({ ...data, email: e.target.value })}
            name="email"
            type="email"
            placeholder="Enter your email"
            className="block w-full px-4 py-3 border-0 rounded-sm text-base text-gray-900 placeholder-gray-500 focus:outline-none hover:ring-2 hover:ring-offset-2 focus:ring-2 focus:ring-offset-2 hover:ring-[#999df6] ring-[#999df6] focus:ring-offset-gray-900"
          />
        </div>
        {isLoading ? (
          <div className="mt-3 sm:mt-0 sm:ml-3 lg:w-[185px] min-h-[36px] text-center relative">
            <Loading isLoading={isLoading} />
          </div>
        ) : (
          <div
            className="mt-3 sm:mt-0 sm:ml-3"
            id="gtm-email-signup-button-outer"
          >
            <Link href="/" legacyBehavior>
              <button
                type="submit"
                id="gtm-email-signup-button-inner"
                onClick={onSubmit}
                className="block w-full py-3 px-8 rounded-sm shadow bg-black border-white border-[1px] border-solid hover:border-[#999df600]
            text-white font-medium
            focus:outline-none hover:ring-2 hover:ring-offset-1 hover:ring-[#999df6] ring-[#999df6] focus:ring-offset-gray-900"
              >
                Stay in the loop
                <ArrowRightIcon className="h-4 m-auto ml-1 inline-block" />
              </button>
            </Link>
          </div>
        )}
      </div>
    </form>
  );
}
