import cn from 'clsx';

export default function StyledInput({
  className,
  props
}: {
  className?: string;
  props: any;
}) {
  return (
    <input
      className={cn(
        className,
        'w-full rounded-md dark:bg-dark',
        'border border-gray-300 dark:border-gray-600',
        'focus:border-primary-300 focus:outline-none focus:ring-0 dark:focus:border-primary-300'
      )}
      {...props}
    />
  );
}
