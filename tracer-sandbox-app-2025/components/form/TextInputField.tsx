import React from 'react';
import cn from 'clsx';

const focusAndHover =
  'focus:outline-none hover:ring-2 hover:ring-offset-2 focus:ring-2 focus:ring-offset-2 ring-[#999df6] focus:ring-offset-gray-900';

export const TextInputField = ({
  name,
  type,
  placeholder,
  className,
  onChange,
  value,
  defaultValue,
  register
}: {
  name: string;
  type: string;
  placeholder?: string;
  className?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  value?: string;
  defaultValue?: string;
  register?: any;
}) => {
  // need to accomodate if there is no react hook form implemented

  return register ? (
    <input
      autoComplete="off"
      defaultValue={defaultValue ? defaultValue : value}
      onChange={onChange}
      name={name || ''}
      type={type || 'text'}
      placeholder={placeholder || ''}
      className={cn(
        'block w-full px-4 py-2 border-0 rounded-sm text-base placeholder-gray-500 ring-gray-900 ring-1',
        focusAndHover,
        className
      )}
      {...register(`${name}`)}
    />
  ) : (
    <input
      autoComplete="off"
      defaultValue={value}
      onChange={onChange}
      name={name || ''}
      type={type || 'text'}
      placeholder={placeholder || ''}
      className={cn(
        'block w-full px-4 py-2 border-0 rounded-sm text-base  placeholder-gray-500 text-black ring-gray-900 ring-1',
        className
      )}
    />
  );
};
