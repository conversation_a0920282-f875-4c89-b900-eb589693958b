import { RecordType } from "src/types";

// api/logs.ts
interface FetchLogsParams {
  timeFilter?: string;
  startTimeFilter?: Date;
  endTimeFilter?: Date;
  teamId?: string;
  recordTypeFilter?: RecordType[];
  serviceName: string;
}

export const fetchLogsApi = async ({
  timeFilter = '',
  startTimeFilter,
  endTimeFilter,
  recordTypeFilter,
  teamId,
  serviceName
}: FetchLogsParams) => {
  return;
  console.log("[components/api/fetchLogsApi] Fetching logs")
  // Validate userId
  if (!teamId || typeof teamId !== 'string' || teamId.trim() === '') {
    console.log(
      '[components/api/fetchLogsApi] Invalid teamId: teamId must be a non-empty string'
    );
    return;
  }

  // Validate activeService
  if (
    !serviceName ||
    typeof serviceName !== 'string' ||
    serviceName.trim() === ''
  ) {
    console.log(
      'Invalid activeService: serviceName must be a non-empty string'
    );
    return;
  }


  // Construct query string
  const queryString = new URLSearchParams({
    timeFilter,
    teamId,
    serviceName,
    startTimeFilter: startTimeFilter?.toISOString() || '',
    endTimeFilter: endTimeFilter?.toISOString() || '',
    recordTypeFilter: recordTypeFilter ? JSON.stringify(recordTypeFilter) : ''
  }).toString();

  console.log(`[components/api/fetchLogsApi] Fetching logs with query string: ${queryString}`)

  try {
    const response = await fetch(`/api/logs?${queryString}`, {
      method: 'GET'
    });

    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    console.log(`Fetch logs failed: ${error.message}`, error);
  }
};
