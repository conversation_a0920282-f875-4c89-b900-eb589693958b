import axios from 'axios';
import { ErrorResponseHandler } from './ErrorResponseHandler';
import { AxiosError } from 'axios';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const formUrlParams = (params: Record<string, any>): string => {
  let urlParams = '';
  Object.entries(params).forEach(([key, value], index) => {
    let encodedValue: string;
    try {
      encodedValue = decodeURIComponent(value);
      encodedValue = encodeURIComponent(encodedValue);
    } catch (error) {
      encodedValue = '';
    }
    if (index === 0) {
      if (encodedValue) {
        urlParams = `?${key}=${encodedValue}`;
      } else {
        urlParams = `?${key}=`;
      }
    } else if (encodedValue) {
      urlParams = `${urlParams}&${key}=${encodedValue}`;
    } else {
      urlParams = `${urlParams}&${key}=`;
    }
  });
  return urlParams;
};

export type Created = 201;

export type Success = 200;

export type Forbidden = 403;

export type BadRequest = 400;

export type Unauthorized = 401;

export type NotFound = 404;

export type Conflict = 409;

export type ServerError = 500;

export type SuccessStatusCode = Created | Success;

export type ErrorStatusCode =
  | Forbidden
  | Forbidden
  | Unauthorized
  | NotFound
  | ServerError
  | BadRequest
  | Conflict;

export enum ErrorType {
  NotFound = 'not_found'
}

export type StatusCode = SuccessStatusCode | ErrorStatusCode;

export type ApiResponse<T> = { data: T };

export interface ErrorResponse {
  statusCode: ErrorStatusCode;
  payload: null;
  error: string;
  message: null;
}

export interface SuccessResponse<T, P = unknown> {
  statusCode: SuccessStatusCode;
  message: string;
  payload: T;
  error: null;
  params?: P;
}

export interface GetTraceItemProps {
  id: string;
  spanId: string | null;
  levelUp: string | null;
  levelDown: string | null;
}

export interface PayloadProps {
  [id: string]: {
    events: any;
    segmentID: string;
    columns: string[];
  };
}

const getTraceItem = async (
  props: GetTraceItemProps
): Promise<SuccessResponse<PayloadProps> | ErrorResponse> => {
  try {
    const response = await axios.request<PayloadProps>({
      url: `/traces/${props.id}${formUrlParams({
        spanId: props.spanId,
        levelUp: props.levelUp,
        levelDown: props.levelDown
      })}`,
      method: 'get'
    });

    return {
      statusCode: 200,
      error: null,
      message: 'Success',
      payload: response.data
    };
  } catch (error) {
    return ErrorResponseHandler(error as AxiosError);
  }
};

export default getTraceItem;
