import React from 'react';
import { ContainerWrapperLp } from '@/components/routes/landing/components/ContainerWrapperLp';

export function Footer({}) {
  // Check if we're on a page that shouldn't display the footer
  const shouldHideFooter = typeof window !== 'undefined' && 
    (window.location.pathname === '/' || window.location.pathname.includes('/landing'));
  
  if (shouldHideFooter) {
    return null; // Don't render anything for the landing page
  }
  
  return (
    <div className="bg-black">
      <ContainerWrapperLp>
        <footer className="bg-primary">
          <div className="py-7 flex flex-col md:flex-row justify-between items-center space-y-4 text-accent-6 text-md">
            <div className="align-center text-center mt-0 hidden">
              "Biology is the most powerful technology ever created. DNA is
              software, protein are hardware, cells are factories"
              <br className="md:hidden" />
              &nbsp;- <PERSON><PERSON><PERSON>
            </div>
          </div>
        </footer>
      </ContainerWrapperLp>
    </div>
  );
}
