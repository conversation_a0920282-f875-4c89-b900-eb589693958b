import { faDna, faImage } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

type IconBgProps = {
  children: React.ReactNode;
  className?: string;
};

type FileIconProps = {
  fileFormat: string;
};

export function IconBg({ children, className }: IconBgProps) {
  return (
    <span
      className={`bg-gradient-to-br flex-shrink-0 flex items-center justify-center w-[30px] h-[26px] rounded-[3px] ${className}`}
    >
      {children}
    </span>
  );
}

export function FileIcon({ fileFormat }: FileIconProps) {
  if (fileFormat === 'image') {
    return (
      <IconBg className="text-left from-[#620007] to-[#e42b25]">
        <FontAwesomeIcon icon={faImage} />
      </IconBg>
    );
  } else if (
    fileFormat === 'genomics' ||
    fileFormat === 'fastq' ||
    fileFormat === 'bam'
  ) {
    // const GRADIENT_PURPLE = 'from-[#6D28D9] to-[#7C3AED]';

    return (
      // <IconBg className="bg-gradient-to-br from-[#007f47] to-[#144c20] text-left">
      <IconBg className="bg-gradient-to-br from-[#6D28D9] to-[#7C3AED]  text-left">
        <FontAwesomeIcon icon={faDna} />
      </IconBg>
    );
  } else {
    return null;
  }
}
