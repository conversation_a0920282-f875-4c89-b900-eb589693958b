interface GrafanaButtonProps {
  onClick: () => void;
  isLoading: boolean;
  isGrafanaSetup: boolean;
}

export function GrafanaButton({ onClick, isLoading, isGrafanaSetup }: GrafanaButtonProps) {
  return (
    <button
      onClick={onClick}
      disabled={isLoading || isGrafanaSetup}
      className="w-full mb-4 px-4 py-2 bg-white text-black rounded hover:bg-gray-100 transition-colors duration-200 font-medium disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
    >
      {isLoading ? (
        <>
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
          Setting up Grafana...
        </>
      ) : isGrafanaSetup ? (
        'Grafana monitoring set up'
      ) : (
        'Set up Grafana monitoring'
      )}
    </button>
  );
}