// import { useUser as useClerkUser } from '@clerk/nextjs';
// import { useState } from 'react';
// import { createApiKey } from '../api-keys-integration/service';
import { GrafanaSetup } from './GrafanaSetup';

export function OnboardingContainer() {
  // const { user } = useClerkUser();

  return (
    <div className="bg-black flex justify-center pt-0 h-screen relative fadeIn duration-300">
      <div className="max-sm:p-4 max-w-3xl w-full">
        <GrafanaSetup />
     
      </div>
    </div>
  );
}
