import React, { useState, useEffect } from 'react';
import { LoginSection } from './components/login/LoginSection';

export const LandingPage = () => {
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true after component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="bg-black min-h-screen overflow-hidden">
      {/* Split layout container */}
      <div className="flex flex-col md:flex-row min-h-screen">
        {/* Left side - Login section */}
        <LoginSection />

        {/* Right side - Dashboard preview */}
        <div className="w-full md:w-[60%] bg-black relative flex items-start justify-center pt-24"> {/* Increased from pt-12 to pt-30 */}
          <div className="max-w-[80%] overflow-hidden rounded-lg border border-gray-800">
            {isClient ? (
              <img 
                src="/assets/tracer-dashboard-preview.png" 
                alt="Dashboard Preview" 
                className="w-full h-auto object-contain"
                onError={(e) => {
                  e.currentTarget.src = "/assets/fallback-dashboard.png";
                }}
              />
            ) : (
              <div className="w-full aspect-video bg-gray-900" /> // Placeholder during server render
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
