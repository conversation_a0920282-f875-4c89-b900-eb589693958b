// import cn from 'clsx';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import { faCopy } from '@fortawesome/free-solid-svg-icons';

// interface CodeExampleProps {
//   className?: string;
//   isFadeInReady: boolean;
// }

// export function CodeButton() {
//   return (
//     <div className="text-right mb-3 text-[15px]">
//       <button>
//         copy code
//         <FontAwesomeIcon icon={faCopy} className="ml-2" />
//       </button>
//     </div>
//   );
// }

// const StyledPurple = ({ text, className }) => (
//   <span className={cn('text-[#c678dd]', className)}>{text}</span>
// );
// const StyledGray = ({ text }) => <span className="text-[#abb2bf]">{text}</span>;
// const StyledOrange = ({ text }) => (
//   <span className="text-[#d19a66]">{text}</span>
// );
// const StyledGreen = ({ text }) => (
//   <span className="text-[#98c379]">{text}</span>
// );

// // Constructs
// const Equals = () => <span className="text-[#abb2bf] mx-3">=</span>;
// const RequirePackage = ({ packageName }: { packageName: string }) => (
//   <>
//     <StyledPurple text="const  " className="mr-1" />
//     <StyledGray text="Netrunner" />
//     <Equals />
//     <StyledOrange text="require" />
//     <StyledGray text="(" />
//     <StyledGreen text={`'${packageName.toLowerCase()}'`} />
//     <StyledGray text=");" />
//   </>
// );

// export const CodeExample = ({ isFadeInReady }: CodeExampleProps) => (
//   <div
//     className={cn(
//       'cursor-pointer opacity-0',
//       isFadeInReady && 'opacity-100 fadeIn2s'
//     )}
//   >
//     <CodeButton />

//     <div className="px-4 py-3 pt-2 border border-white rounded-[1px] bg-brandBlack text-[16px]">
//       <RequirePackage packageName="Netrunner" />
//       <br />
//       <StyledGray text="Netrunner.auth({ " />
//       <StyledOrange text="apiKey" />
//       <StyledGray text=":   " />
//       <StyledGreen text="your_secret_key" />
//       <StyledGray text="   })" />
//       <br />
//       <StyledGray text="Netrunner.push({  file, name  });" />
//     </div>
//   </div>
// );
