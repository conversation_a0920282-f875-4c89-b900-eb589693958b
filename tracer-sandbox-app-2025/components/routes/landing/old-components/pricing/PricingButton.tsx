// import React from 'react';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import { faArrowRightLong } from '@fortawesome/free-solid-svg-icons';

// type PricingButtonProps = {
//   onClick?: () => void;
//   className?: string;
//   text: string;
// };

// export function PricingButton({
//   onClick,
//   className,
//   text
// }: PricingButtonProps) {
//   return (
//     <button
//       className={`font-inter text-white ml-4 mt-5 text-[17px] px-[20px] py-[13px] rounded-sm bg-gradient-to-r from-blue-900 to-blue-500 ${className}`}
//       onClick={onClick}
//     >
//       {text}
//       <FontAwesomeIcon icon={faArrowRightLong} className="ml-2" />
//     </button>
//   );
// }
