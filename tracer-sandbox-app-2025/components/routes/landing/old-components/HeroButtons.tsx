// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import cn from 'clsx';
// import React from 'react';
// import { faBookOpen, faChevronRight } from '@fortawesome/free-solid-svg-icons';

// export const HeroButton = ({
//   text,
//   className,
//   onClick,
//   icon
// }: {
//   text: string;
//   className?: string;
//   onClick?: () => void;
//   icon?: any;
// }) => {
//   return (
//     <button
//       className={cn('px-[20px] py-[10px] rounded-sm', className)}
//       onClick={onClick}
//     >
//       {text}&nbsp;
//       {icon && (
//         <FontAwesomeIcon
//           icon={icon}
//           className="text-[14px] ml-[1px] mb-[1px]"
//         />
//       )}
//     </button>
//   );
// };

// export function HeroButtons({
//   isVisible,
//   handleButtonClick
// }: {
//   isVisible: boolean;
//   handleButtonClick: any;
// }) {
//   return (
//     isVisible && (
//       <div className="grid grid-cols-2 gap-3 sm:max-w-[380px]">
//         <HeroButton
//           text="Start building"
//           onClick={() => handleButtonClick()}
//           icon={faChevronRight}
//           className="max-sm:text-[16px] text-white bg-brandPurple"
//         />
//         <HeroButton
//           text="Documentation"
//           onClick={() => handleButtonClick()}
//           icon={faBookOpen}
//           className="max-sm:text-[16px] font-bold bg-white text-gray-800"
//         />
//       </div>
//     )
//   );
// }
