// import React from 'react';
// import { LpTitle } from '../TitleLp';
// import { arrayOfValuePropositions } from './constants';
// import { ContainerWrapperLp } from '../../components/ContainerWrapperLp';
// import { ValuePropCard } from './ValuePropCard';
// import { BlueIconBg } from '../../../demo/components/SummaryDropdown';
// import { SubTitleLp } from '../SubtitleLp';

// export function ValuePropositions() {
//   return (
//     <div className="bg-white text-[18px] max-md:pt-10">
//       <ContainerWrapperLp className="py-20 bg-white text-black px-16">
//         <LpTitle>Build faster and more secure</LpTitle>
//         <SubTitleLp
//           text="Because every application needs storage, and you shouldn't have to be
//           a security expert to ship"
//         />
//         <div className="grid md:grid-cols-3 gap-6">
//           {arrayOfValuePropositions.map((prop, index) => (
//             <ValuePropCard
//               key={index}
//               icon={
//                 <BlueIconBg className="h-10 w-10">
//                   <prop.Icon className="h-6 w-6 text-white font-medium" />
//                 </BlueIconBg>
//               }
//               label={prop.label}
//               title={prop.title}
//               text={prop.text}
//             />
//           ))}
//         </div>
//       </ContainerWrapperLp>
//     </div>
//   );
// }
