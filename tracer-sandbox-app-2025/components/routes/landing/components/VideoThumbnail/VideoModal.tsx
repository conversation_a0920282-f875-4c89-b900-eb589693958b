import React from 'react';
import { Dialog } from '@headlessui/react';

type VideoModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const VideoModal: React.FC<VideoModalProps> = ({ isOpen, onClose }) => {
  const replitVideoId = 'D4f7_lPwXtE';
  const netrunnerVideoId = '7AjYX6NdTag';
  const netrunnerDemoVideoId = '3445';
  const VIDEO_ID = netrunnerVideoId || replitVideoId || netrunnerDemoVideoId;
  https: return (
    <Dialog
      as="div"
      className="fixed inset-0 z-10 overflow-y-auto"
      open={isOpen}
      onClose={onClose}
    >
      <div className="flex items-center justify-center min-h-screen">
        <Dialog.Overlay className="fixed inset-0 bg-black opacity-50" />
        <div className="inline-flex flex-col items-center justify-center z-10">
          <div className="inline-block w-full max-w-screen-lg overflow-hidden align-middle transition-all transform shadow-xl rounded-2xl">
            <iframe
              className="w-full h-[56.25%] md:w-[950px] md:h-[559px] min-h-[250px] min-w-[350px] rounded" // 16:9 aspect ratio
              src={`https://www.youtube.com/embed/${VIDEO_ID}?autoplay=1`}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default VideoModal;
