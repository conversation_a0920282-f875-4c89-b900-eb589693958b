import React from 'react';
import { CAccent } from '@/components/ui/CAccent';
import cn from 'clsx';

function DesktopLayout({ children }) {
  return (
    <div className="hidden md:grid grid-cols-12">
      <div className="col-start-3 col-end-11 px-10">{children}</div>
    </div>
  );
}

function MobileLayout({ children }) {
  return (
    <section className="mm-auto fade-in-start md:hidden">{children}</section>
  );
}

function SectionTitle({ children }) {
  return (
    <h1
      className="mt-7 mb-3 max-sm:mt-20 text-4xl font-semibold max-sm:text-4xl"
      data-fade="1"
    >
      <CAccent>{children}</CAccent>
    </h1>
  );
}

function Paragraph({
  children,
  className
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <p
      data-fade="3"
      className={cn(className, 'mb-4 text-[16px] text-white/90')}
    >
      {children}
    </p>
  );
}

function AdvantageList() {
  const advantages = [
    {
      title: 'Service Range',
      description:
        "Most developer platforms offer around 10 services, constraining customization and force the use of a serverless architecture. Netrunner operates synergistically with AWS and GCP's ~250 service offering."
    },
    {
      title: 'No More Tool Fatigue',
      description:
        " Instead of working around Firebase's limited service offering by cobbling together solutions like SendGrid for emails or PlanetScale for SQL databases, public clouds enable you to put everything under one roof "
    },
    {
      title: 'Built for Tomorrow',
      description:
        'As costs eventually increase and API/database performance declines, migration to larger cloud platforms becomes inevitable, why not get it right from the beginning?'
    },
    {
      title: 'Security',
      description:
        'Netrunner takes advantage of the robust security protocols, identity, and access controls that major clouds offer, essential for business apps and team collaborations.'
    }
  ];

  return (
    <div className="text-[16px] text-white/90 space-y-7">
      {advantages.map((advantage) => (
        <div className="flex" key={advantage.title}>
          <span className="font-semibold text-gray-300 mr-2">•</span>
          <div>
            <span className="underline">{advantage.title}:</span>
            <br />
            {advantage.description}
          </div>
        </div>
      ))}
    </div>
  );
}

function StyledLink({ href, children }) {
  return (
    <a
      href={href}
      className="cursor-pointer underline hover:opacity-60 decoration-[#999df6] hover:decoration-cyan-300 decoration-2 decoration-dashed underline-offset-4"
    >
      {children}
    </a>
  );
}
function CloudOptionsList() {
  const cloudOptions = [
    {
      title: 'User-friendly Developer Platforms',
      description:
        'Like Firebase/Supabase, which offer ease-of-use and productivity but lack flexibility.'
    },
    {
      title: 'Large Public Clouds',
      description:
        'Which are scalable but complex and time-consuming to operate.'
    }
  ];

  return (
    <div className="text-[16px] text-white/90 space-y-3 mb-6">
      {cloudOptions.map((option) => (
        <div className="flex" key={option.title}>
          <span className="font-semibold text-gray-300 mr-2">•</span>
          <div>
            <span className="underline">{option.title}:</span>
            <br />
            {option.description}
          </div>
        </div>
      ))}
    </div>
  );
}

function SectionContent() {
  return (
    <section className="mm-auto fade-in-start">
      <div className="layout min-h-main pb-12 mb:py-12 md:py-20 fadeIn text-white">
        <SectionTitle>
          We Are On A Mission To Turn AWS Into A Personalized Firebase
        </SectionTitle>

        <Paragraph>
          Netrunner enables application developers to rapidly create and
          integrate cloud infrastructure. It transforms your AWS cloud account
          into a personalised Firebase-like experience.
        </Paragraph>

        <SectionTitle>
          Bridging The Gap Between Public Clouds And Developer Platforms
        </SectionTitle>

        <Paragraph>
          Developers have to navigate between two cloud infrastructure options:
        </Paragraph>

        <CloudOptionsList />

        <Paragraph
        // className="cursor-pointer underline hover:opacity-60 decoration-[#999df6] hover:decoration-cyan-300 decoration-2 decoration-dashed underline-offset-4"
        >
          This is a constrained and archaic way of thinking.
        </Paragraph>
        <Paragraph>
          We should have the freedom to harness the full power of the cloud but
          with an improved user experience as on application development
          platforms.
        </Paragraph>

        <Paragraph>
          Netrunner is the synthesis of these beliefs. It integrates directly
          with your cloud account and automates the provisioning of
          infrastructure. You can launch new back-end services in a few clicks
          and generate instant APIs.
        </Paragraph>

        <SectionTitle>Core Advantages</SectionTitle>
        <em className="hidden">
          "[small todo is to add a bit more text inside each bullet point (or
          therafter) how Netrunner helps you do this]"
        </em>
        <Paragraph>
          The goal is to improve in four different ways over the legacy
          application development platforms out there:
        </Paragraph>

        <AdvantageList />

        <SectionTitle>Let's Build Together</SectionTitle>

        <Paragraph>
          The mission is thus to create a new platform to combine the developer
          experience of Firebase and combine it with automated provisioning
          services for public clouds.
        </Paragraph>
        <Paragraph>
          We want to become the fastest way to implement public cloud services
          into your application.
        </Paragraph>

        <Paragraph>
          Ambitious? Yes. Impossible? Not if we join forces. You can help out by
          diving into the{' '}
          <StyledLink href="https://github.com/netrunnerhq">
            GitHub project
          </StyledLink>{' '}
          and share your thoughts in a pull request. Let’s launch this. Ping me{' '}
          <StyledLink href="https://twitter.com/vincent_hus">
            @jvf_hus
          </StyledLink>
        </Paragraph>
      </div>
    </section>
  );
}

export default function Article() {
  return (
    <div className="px-5 md:px-0">
      <DesktopLayout>
        <SectionContent />
      </DesktopLayout>

      <MobileLayout>
        <SectionContent />
      </MobileLayout>
    </div>
  );
}
