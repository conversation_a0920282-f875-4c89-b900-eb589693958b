'use client';

import { useClerk } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import <PERSON>rip<PERSON> from 'next/script';
import { useEffect } from 'react';

// Declare global window type for Google
declare global {
  interface Window {
    google: any;
  }
}

export function CustomGoogleOneTap({ children }: { children: React.ReactNode }) {
  const clerk = useClerk();
  const router = useRouter();

  const handleCredential = async (token: string) => {
    try {
      const res = await clerk.authenticateWithGoogleOneTap({ token });
      await clerk.handleGoogleOneTapCallback(res, {
        afterSignInUrl: '/terms',
      });
    } catch (error) {
      console.error('Authentication error:', error);
      router.push('/sign-in');
    }
  };

  const initializeOneTap = () => {
    const { google } = window;
    if (!google) return console.log('Google script did not load properly.');

    google.accounts.id.initialize({
      client_id: '************-6fg1pphiue1fnbglkeat34c1ae7bbnku.apps.googleusercontent.com',
      callback: async (response: any) => {
        console.log('callback called')
        await handleCredential(response.credential);
      },
    });

    google.accounts.id.prompt((notification: any) => {
      if (notification.isNotDisplayed()) {
        console.log('Not displayed reason:', notification.getNotDisplayedReason());
      } else if (notification.isSkippedMoment()) {
        console.log('Skipped reason:', notification.getSkippedReason());
      } else if (notification.isDismissedMoment()) {
        console.log('Dismissed reason:', notification.getDismissedReason());
      }
    });
  };

  return (
    <>
      <Script
        src="https://accounts.google.com/gsi/client"
        strategy="afterInteractive"
        onLoad={initializeOneTap}  // Wait until script fully loads before initializing
      />
      {children}
    </>
  );
}
