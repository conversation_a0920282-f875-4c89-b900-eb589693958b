import React from 'react';
import cn from 'clsx';

interface GridWrapperLpProps {
  children: React.ReactNode;
  className?: string;
  gridConfig?: 'default' | 'wide';
}

export const GridWrapperLp: React.FC<GridWrapperLpProps> = ({
  children,
  className,
  gridConfig = 'default'
}) => {
  return (
    <div className={cn('grid grid-cols-12', className)}>
      {gridConfig === 'default' ? (
        <div className="col-start-3 col-end-10">{children}</div>
      ) : (
        <div className="col-start-2 col-end-12">{children}</div>
      )}
    </div>
  );
};
