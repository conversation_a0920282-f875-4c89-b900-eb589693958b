import React from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import cn from 'clsx';

interface CardProps {
  chipText: string;
  title: string;
  content: string;
  buttonLabel: string;
  buttonType: 'blue' | 'gray';
  icon: any;
  path: string;
  isIconVisible?: boolean;
}

export const Card: React.FC<CardProps> = ({
  chipText,
  title,
  content,
  buttonLabel,
  buttonType,
  icon,
  path,
  isIconVisible = true
}) => (
  <Link href={path} className="max-sm:mb-10">
    <div className="flex flex-col justify-between p-6 max-lg:p-3 max-lg:py-4 max-sm:p-3 text-left lg:text-lg bg-[#1E1D1E] max-h-[350px] h-full max-sm:mb-8 rounded-lg cursor-pointer lineHeightCardsHero">
      <div>
        <Chip text={chipText} className="mb-2" />
        <h3 className="lg:my-5 md:my-2 font-medium text-white lg:text-[23px] max-sm:my-2 lg:min-h-[56px]">
          {title}
        </h3>
        <p className="text-[#90959D] text-[15px] lg:text-[18px] max-sm:-mb-[15px]">
          {content}
        </p>
      </div>
      <CardButton
        text={buttonLabel}
        type={buttonType}
        icon={icon}
        isIconVisible={isIconVisible}
      />
    </div>
  </Link>
);

interface ChipProps {
  text: string;
  className?: string;
}

const Chip: React.FC<ChipProps> = ({ text }) => (
  <h3
    className={cn(
      `inline-flex mb-1 font-medium text-white ${
        text === 'NEW' ? 'bg-blue-700 px-3 py-[2px]' : 'opacity-70'
      }`,
      'mb-2'
    )}
  >
    {text}
  </h3>
);

interface CardButtonProps {
  type: 'blue' | 'gray';
  text: string;
  icon: any;
  isIconVisible?: boolean;
}

const CardButton: React.FC<CardButtonProps> = ({
  type,
  text,
  icon,
  isIconVisible = true
}) => (
  <button
    className={`font-inter text-white w-full md:mt-5 text-[17px] px-[20px] py-[10px] rounded-sm ${
      type === 'blue'
        ? 'bg-gradient-to-r from-blue-900/80 to-blue-700'
        : 'bg-gradient-to-r from-black/90 to-gray-700'
    }`}
  >
    {text}
    {isIconVisible && <FontAwesomeIcon icon={icon} className="ml-2" />}
  </button>
);
