import cn from 'clsx';

interface IInputProps {
  type: string;
  placeholder?: string;
  className?: string;
  onClick?: () => void;
  defaultValue?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  name: string;
  value?: string;
  ref?: React.Ref<HTMLInputElement> | any;
  register?: any;
}

export function InputField(props: IInputProps) {
  const {
    type,
    placeholder,
    className,
    onClick,
    onChange,
    name,
    value,
    register
  } = props;

  const inputClasses = cn(`
    text-white text-sm relative m-0 -mr-px block 
    rounded border border-solid border-[#c1c9d0]/[.3] 
    bg-transparent bg-clip-padding px-3 py-1 font-normal outline-none 
    transition duration-300 ease-in-out focus:border-primary 
    focus:text-white/80 focus:shadow-te-primary focus:outline-none 
    dark:text-white/80 dark:placeholder:text-white/80 
    ${className}
  `);

  return (
    <input
      type={type}
      className={inputClasses}
      placeholder={placeholder}
      aria-label={placeholder}
      aria-describedby="button-addon1"
      onClick={onClick}
      value={value}
      onChange={onChange}
      name={name}
      {...register}
    />
  );
}
