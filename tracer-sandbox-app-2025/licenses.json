{"@aws-sdk/client-s3@3.651.1": {"licenses": "Apache-2.0", "repository": "https://github.com/aws/aws-sdk-js-v3", "publisher": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@aws-sdk/client-s3", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@aws-sdk/client-s3/LICENSE"}, "@aws-sdk/s3-request-presigner@3.651.1": {"licenses": "Apache-2.0", "repository": "https://github.com/aws/aws-sdk-js-v3", "publisher": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@aws-sdk/s3-request-presigner", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@aws-sdk/s3-request-presigner/LICENSE"}, "@chakra-ui/next-js@2.2.0": {"licenses": "MIT", "repository": "https://github.com/chakra-ui/chakra-ui", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@chakra-ui/next-js", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@chakra-ui/next-js/LICENSE"}, "@chakra-ui/react@2.8.2": {"licenses": "MIT", "repository": "https://github.com/chakra-ui/chakra-ui", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@chakra-ui/react", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@chakra-ui/react/LICENSE"}, "@clerk/nextjs@4.31.5": {"licenses": "MIT", "repository": "https://github.com/clerk/javascript", "publisher": "Clerk", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@clerk/nextjs", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@clerk/nextjs/LICENSE"}, "@clickhouse/client@1.6.0": {"licenses": "Apache-2.0", "repository": "https://github.com/ClickHouse/clickhouse-js", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@clickhouse/client", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@clickhouse/client/LICENSE"}, "@emotion/react@11.13.3": {"licenses": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/react", "publisher": "Emotion Contributors", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@emotion/react", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@emotion/react/LICENSE"}, "@emotion/styled@11.13.0": {"licenses": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/styled", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@emotion/styled", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@emotion/styled/LICENSE"}, "@fortawesome/fontawesome-svg-core@6.6.0": {"licenses": "MIT", "repository": "https://github.com/FortAwesome/Font-Awesome", "publisher": "The Font Awesome Team", "url": "https://github.com/orgs/FortAwesome/people", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/fontawesome-svg-core", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/fontawesome-svg-core/LICENSE.txt"}, "@fortawesome/free-brands-svg-icons@6.6.0": {"licenses": "(CC-BY-4.0 AND MIT)", "repository": "https://github.com/FortAwesome/Font-Awesome", "publisher": "The Font Awesome Team", "url": "https://github.com/orgs/FortAwesome/people", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/free-brands-svg-icons", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/free-brands-svg-icons/LICENSE.txt"}, "@fortawesome/free-solid-svg-icons@6.6.0": {"licenses": "(CC-BY-4.0 AND MIT)", "repository": "https://github.com/FortAwesome/Font-Awesome", "publisher": "The Font Awesome Team", "url": "https://github.com/orgs/FortAwesome/people", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/free-solid-svg-icons", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/free-solid-svg-icons/LICENSE.txt"}, "@fortawesome/react-fontawesome@0.2.2": {"licenses": "MIT", "repository": "https://github.com/FortAwesome/react-fontawesome", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/react-fontawesome", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@fortawesome/react-fontawesome/LICENSE.txt"}, "@headlessui/react@1.7.19": {"licenses": "MIT", "repository": "https://github.com/tailwindlabs/headlessui", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@headlessui/react", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@headlessui/react/README.md"}, "@heroicons/react@1.0.6": {"licenses": "MIT", "repository": "https://github.com/tailwindlabs/heroicons", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@heroicons/react"}, "@mui/material@5.16.7": {"licenses": "MIT", "repository": "https://github.com/mui/material-ui", "publisher": "MUI Team", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@mui/material", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@mui/material/LICENSE"}, "@radix-ui/colors@0.1.9": {"licenses": "MIT", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/colors", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/colors/LICENSE"}, "@radix-ui/react-dropdown-menu@2.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-dropdown-menu", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-dropdown-menu/README.md"}, "@radix-ui/react-icons@1.3.0": {"licenses": "MIT", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-icons", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-icons/LICENSE"}, "@radix-ui/react-popover@1.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-popover", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-popover/README.md"}, "@radix-ui/react-progress@1.1.0": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-progress", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-progress/README.md"}, "@radix-ui/react-scroll-area@1.1.0": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-scroll-area", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-scroll-area/README.md"}, "@radix-ui/react-select@1.2.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-select", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-select/README.md"}, "@radix-ui/react-slot@1.1.0": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-slot", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@radix-ui/react-slot/README.md"}, "@stitches/react@1.2.8": {"licenses": "MIT", "repository": "https://github.com/modulz/stitches", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@stitches/react", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@stitches/react/README.md"}, "@stripe/react-stripe-js@2.8.0": {"licenses": "MIT", "repository": "https://github.com/stripe/react-stripe-js", "publisher": "Stripe", "url": "https://www.stripe.com", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@stripe/react-stripe-js", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@stripe/react-stripe-js/LICENSE"}, "@stripe/stripe-js@1.54.2": {"licenses": "MIT", "publisher": "Stripe", "url": "https://www.stripe.com", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@stripe/stripe-js", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@stripe/stripe-js/LICENSE"}, "@tailwindcss/aspect-ratio@0.4.2": {"licenses": "MIT", "repository": "https://github.com/tailwindlabs/tailwindcss-aspect-ratio", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@tailwindcss/aspect-ratio", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@tailwindcss/aspect-ratio/README.md"}, "@tanstack/react-table@8.20.5": {"licenses": "MIT", "repository": "https://github.com/TanStack/table", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@tanstack/react-table", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@tanstack/react-table/LICENSE"}, "@tremor/react@3.18.1": {"licenses": "Apache*", "repository": "https://github.com/tremorlabs/tremor", "publisher": "tremor", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@tremor/react", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@tremor/react/License"}, "@xata.io/client@0.29.5": {"licenses": "Apache-2.0", "repository": "https://github.com/xataio/client-ts", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/@xata.io/client", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/@xata.io/client/LICENSE"}, "axios@1.7.7": {"licenses": "MIT", "repository": "https://github.com/axios/axios", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/axios", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/axios/LICENSE"}, "canvas-confetti@1.9.3": {"licenses": "ISC", "repository": "https://github.com/catdad/canvas-confetti", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/canvas-confetti", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/canvas-confetti/LICENSE"}, "chart.js@4.4.4": {"licenses": "MIT", "repository": "https://github.com/chartjs/Chart.js", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/chart.js", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/chart.js/LICENSE.md"}, "chartjs-adapter-date-fns@3.0.0": {"licenses": "MIT", "repository": "https://github.com/chartjs/chartjs-adapter-date-fns", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-adapter-date-fns", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-adapter-date-fns/LICENSE.md"}, "chartjs-adapter-luxon@1.3.1": {"licenses": "MIT", "repository": "https://github.com/chartjs/chartjs-adapter-luxon", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-adapter-luxon", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-adapter-luxon/LICENSE.md"}, "chartjs-plugin-streaming@2.0.0": {"licenses": "MIT", "repository": "https://github.com/nagix/chartjs-plugin-streaming", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://nagix.github.io/", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-plugin-streaming", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-plugin-streaming/LICENSE.md"}, "chartjs-plugin-zoom@2.0.1": {"licenses": "MIT", "repository": "https://github.com/chartjs/chartjs-plugin-zoom", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-plugin-zoom", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/chartjs-plugin-zoom/LICENSE.md"}, "class-variance-authority@0.7.0": {"licenses": "Apache-2.0", "repository": "https://github.com/joe-bell/cva", "publisher": "<PERSON>", "url": "https://joebell.co.uk", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/class-variance-authority", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/class-variance-authority/README.md"}, "clsx@1.2.1": {"licenses": "MIT", "repository": "https://github.com/lukeed/clsx", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/clsx", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/clsx/license"}, "date-fns@3.6.0": {"licenses": "MIT", "repository": "https://github.com/date-fns/date-fns", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/date-fns", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/date-fns/LICENSE.md"}, "dayjs@1.11.13": {"licenses": "MIT", "repository": "https://github.com/iamkun/dayjs", "publisher": "i<PERSON><PERSON>n", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/dayjs", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/dayjs/LICENSE"}, "eslint-config-next@14.2.11": {"licenses": "MIT", "repository": "https://github.com/vercel/next.js", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/eslint-config-next"}, "eslint-config-prettier@8.10.0": {"licenses": "MIT", "repository": "https://github.com/prettier/eslint-config-prettier", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/eslint-config-prettier", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/eslint-config-prettier/LICENSE"}, "form-data@4.0.0": {"licenses": "MIT", "repository": "https://github.com/form-data/form-data", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/form-data", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/form-data/License"}, "framer-motion@10.18.0": {"licenses": "MIT", "repository": "https://github.com/framer/motion", "publisher": "Framer", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/framer-motion", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/framer-motion/LICENSE.md"}, "konva@8.4.3": {"licenses": "MIT", "repository": "https://github.com/konvajs/konva", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/konva", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/konva/LICENSE"}, "license-checker@25.0.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/davglass/license-checker", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/license-checker", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/license-checker/LICENSE"}, "lodash@4.17.21": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/lodash", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/lodash/LICENSE"}, "logrocket@8.1.3": {"licenses": "MIT", "publisher": "LogRocket", "email": "<EMAIL>", "url": "https://logrocket.com/", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/logrocket", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/logrocket/README.md"}, "lucide-react@0.358.0": {"licenses": "ISC", "repository": "https://github.com/lucide-icons/lucide", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/lucide-react", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/lucide-react/LICENSE"}, "luxon@3.5.0": {"licenses": "MIT", "repository": "https://github.com/moment/luxon", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/luxon", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/luxon/LICENSE.md"}, "mailgun.js@10.2.3": {"licenses": "MIT", "repository": "https://github.com/mailgun/mailgun.js", "publisher": "Mailgun", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/mailgun.js", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/mailgun.js/README.md"}, "moment@2.30.1": {"licenses": "MIT", "repository": "https://github.com/moment/moment", "publisher": "Iskren Ivov Chernev", "email": "<EMAIL>", "url": "https://github.com/ichernev", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/moment", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/moment/LICENSE"}, "nanoid@5.0.7": {"licenses": "MIT", "repository": "https://github.com/ai/nanoid", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/nanoid", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/nanoid/LICENSE"}, "next@14.2.11": {"licenses": "MIT", "repository": "https://github.com/vercel/next.js", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/next", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/next/license.md"}, "openai@4.61.0": {"licenses": "Apache-2.0", "repository": "https://github.com/openai/openai-node", "publisher": "OpenAI", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/openai", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/openai/LICENSE"}, "react-day-picker@8.10.1": {"licenses": "MIT", "repository": "https://github.com/gpbl/react-day-picker", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-day-picker", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-day-picker/LICENSE"}, "react-dom@18.3.1": {"licenses": "MIT", "repository": "https://github.com/facebook/react", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-dom", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-dom/LICENSE"}, "react-hook-form@7.53.0": {"licenses": "MIT", "repository": "https://github.com/react-hook-form/react-hook-form", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-hook-form", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-hook-form/LICENSE"}, "react-konva-utils@0.3.2": {"licenses": "MIT", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-konva-utils", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-konva-utils/LICENSE"}, "react-konva@18.2.10": {"licenses": "MIT", "repository": "https://github.com/konvajs/react-konva", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-konva", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-konva/LICENSE"}, "react-syntax-highlighter@15.5.0": {"licenses": "MIT", "repository": "https://github.com/react-syntax-highlighter/react-syntax-highlighter", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-syntax-highlighter", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/react-syntax-highlighter/LICENSE"}, "react@18.3.1": {"licenses": "MIT", "repository": "https://github.com/facebook/react", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/react", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/react/LICENSE"}, "reactflow@11.11.4": {"licenses": "MIT", "repository": "https://github.com/xyflow/xyflow", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/reactflow", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/reactflow/LICENSE"}, "sass@1.78.0": {"licenses": "MIT", "repository": "https://github.com/sass/dart-sass", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nex3", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/sass", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/sass/LICENSE"}, "stripe@12.18.0": {"licenses": "MIT", "repository": "https://github.com/stripe/stripe-node", "publisher": "Stripe", "email": "<EMAIL>", "url": "https://stripe.com/", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/stripe", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/stripe/LICENSE"}, "styled-components@6.1.13": {"licenses": "MIT", "repository": "https://github.com/styled-components/styled-components", "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/styled-components", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/styled-components/LICENSE"}, "swr@2.2.5": {"licenses": "MIT", "repository": "https://github.com/vercel/swr", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/swr", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/swr/LICENSE"}, "tailwind-merge@2.5.2": {"licenses": "MIT", "repository": "https://github.com/dcastil/tailwind-merge", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/tailwind-merge", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/tailwind-merge/LICENSE.md"}, "tailwindcss-animate@1.0.7": {"licenses": "MIT", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/tailwindcss-animate", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/tailwindcss-animate/LICENSE"}, "tracer-web-client@0.1.1": {"licenses": "UNLICENSED", "private": true, "publisher": "<PERSON>", "path": "/Users/<USER>/code/tracer/tracer-app", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/README.md"}, "use-image@1.1.1": {"licenses": "MIT", "repository": "https://github.com/konvajs/use-image", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/use-image", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/use-image/LICENSE"}, "uuid@9.0.1": {"licenses": "MIT", "repository": "https://github.com/uuidjs/uuid", "path": "/Users/<USER>/code/tracer/tracer-app/node_modules/uuid", "licenseFile": "/Users/<USER>/code/tracer/tracer-app/node_modules/uuid/LICENSE.md"}}