import {
  DEFAULT_SECURITY_POLICY,
  IPolicy,
  IPolicyRule
} from 'constants/security/policies';
import {
  createContext,
  useContext,
  useMemo,
  useCallback,
  useReducer
} from 'react';
import { IApplicationConfig } from 'constants/security';
import { _addRuleToPolicy } from './utils/addPolicyRule';
import { _deleteRuleFromPolicy } from './utils/deletePolicyRule';

interface IState {
  selectedApplicationConfig: IApplicationConfig;
  policy: IPolicy;
  activeBucketName: string;
  activeBucketRegion: string;
}

interface IAction {
  type: string;
  payload: any;
}

interface IContextValue extends IState {
  setSelectedApplicationConfig: (template: IApplicationConfig) => void;
  setPolicy: (policy: IPolicy) => void;
  addRuleToPolicy: (params: IPolicyRule) => void;
  deleteRuleFromPolicy: (ruleId: string) => void;
  setActiveBucket: (
    activeBucketName: string,
    activeBucketRegion: string
  ) => void;
}

export const AppContext = createContext<IContextValue>({
  selectedApplicationConfig: null,
  policy: DEFAULT_SECURITY_POLICY,
  activeBucketName: '',
  activeBucketRegion: '',
  setSelectedApplicationConfig: () => null,
  setPolicy: () => null,
  addRuleToPolicy: () => null,
  deleteRuleFromPolicy: () => null,
  setActiveBucket: () => null
});

const REDUCER_ACTIONS = {
  SELECT_APPLICATION_CONFIG: 'SELECT_APPLICATION_CONFIG',
  SET_POLICY: 'SET_POLICY',
  ADD_RULE_TO_POLICY: 'ADD_RULE_TO_POLICY',
  DELETE_RULE_FROM_POLICY: 'DELETE_RULE_FROM_POLICY',
  SET_ACTIVE_BUCKET: 'SET_ACTIVE_BUCKET'
};

export const {
  SELECT_APPLICATION_CONFIG,
  SET_POLICY,
  ADD_RULE_TO_POLICY,
  DELETE_RULE_FROM_POLICY,
  SET_ACTIVE_BUCKET
} = REDUCER_ACTIONS;

function storageReducer(state: IState, action: IAction) {
  switch (action.type) {
    case SELECT_APPLICATION_CONFIG:
      const { selectedApplicationConfig } = action.payload;
      return {
        ...state,
        selectedApplicationConfig
      };
    case SET_POLICY:
      const { policy } = action.payload;
      return {
        ...state,
        policy
      };
    case ADD_RULE_TO_POLICY:
      const policyWithNewRule = _addRuleToPolicy(
        state.policy,
        action.payload.rule
      );
      return {
        ...state,
        policy: policyWithNewRule
      };
    case DELETE_RULE_FROM_POLICY:
      const { ruleId } = action.payload;
      const policyWithRuleDeleted = _deleteRuleFromPolicy(state.policy, ruleId);
      return {
        ...state,
        policy: policyWithRuleDeleted
      };
    case SET_ACTIVE_BUCKET:
      const { activeBucketName, activeBucketRegion } = action.payload;
      if (!activeBucketName || !activeBucketRegion) {
        return {
          ...state,
          activeBucketName: '',
          activeBucketRegion: ''
        };
      }
      return {
        ...state,
        activeBucketName,
        activeBucketRegion
      };
    default:
      return state;
  }
}

export const AppContextProvider = ({ children }) => {
  const [state, dispatch] = useReducer(storageReducer, {
    selectedApplicationConfig: null,
    policy: DEFAULT_SECURITY_POLICY,
    activeBucketName: '',
    activeBucketRegion: ''
  });

  const setActiveBucket = useCallback(
    (activeBucketName: string, activeBucketRegion: string) => {
      dispatch({
        type: SET_ACTIVE_BUCKET,
        payload: { activeBucketName, activeBucketRegion }
      });
    },
    [dispatch]
  );

  const setSelectedApplicationConfig = useCallback(
    (selectedApplicationConfig: IApplicationConfig) => {
      dispatch({
        type: SELECT_APPLICATION_CONFIG,
        payload: { selectedApplicationConfig }
      });
    },
    [dispatch]
  );

  const addRuleToPolicy = useCallback(
    (rule: IPolicyRule) => {
      dispatch({
        type: ADD_RULE_TO_POLICY,
        payload: { rule }
      });
    },
    [dispatch]
  );

  const deleteRuleFromPolicy = useCallback(
    (ruleId: string) => {
      dispatch({
        type: DELETE_RULE_FROM_POLICY,
        payload: { ruleId }
      });
    },
    [dispatch]
  );

  const setPolicy = useCallback(
    (policy: IPolicy) => {
      dispatch({
        type: SET_POLICY,
        payload: { policy }
      });
    },
    [dispatch]
  );

  const value = useMemo<IContextValue>(
    () => ({
      ...state,
      setSelectedApplicationConfig,
      addRuleToPolicy,
      setPolicy,
      deleteRuleFromPolicy,
      setActiveBucket
    }),
    [
      state,
      setSelectedApplicationConfig,
      setPolicy,
      addRuleToPolicy,
      deleteRuleFromPolicy,
      setActiveBucket
    ]
  );

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export function useAppContext(): IContextValue {
  return useContext<IContextValue>(AppContext);
}
