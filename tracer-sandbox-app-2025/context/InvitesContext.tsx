import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState
} from 'react';
import { PendingInvitation } from 'src/types';
import { useTeamsContext } from './TeamsContext';
import { useUserContext } from './UserContext';

export const inviteUserApi = async (teamId: string, email: string) => {
  const response = await fetch(`/api/teams/invite`, {
    method: 'POST',
    body: JSON.stringify({ teamId, email }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export const acceptInviteApi = async (teamId: string) => {
  const response = await fetch(`/api/teams/invite`, {
    method: 'PATCH',
    body: JSON.stringify({ teamId }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export const declineInviteApi = async (teamId: string) => {
  const response = await fetch(`/api/teams/invite`, {
    method: 'DELETE',
    body: JSON.stringify({ teamId }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export const applyInviteCodeApi = async (inviteCode: string) => {
  const response = await fetch(`/api/teams/invite-code`, {
    method: 'POST',
    body: JSON.stringify({ code: inviteCode }),
    headers: {
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data;
};

export interface IInvitesContext {
  inviteUser: (
    email: string
  ) => Promise<{ success: boolean; type: 'EXTERNAL' | 'REGISTERED_USER' }>;
  acceptInvite: (teamId: string) => Promise<void>;
  declineInvite: (teamId: string) => Promise<void>;
  applyInviteCode: (inviteCode: string) => Promise<{ success: boolean }>;
  pendingInvites: PendingInvitation[];
  isLoading: boolean;
  forceRefresh: () => void;
  error: string | null;
}

const InvitesContext = createContext<IInvitesContext | undefined>(undefined);

export const InvitesContextProvider = ({
  children
}: {
  children: React.ReactNode;
}) => {
  const [pendingInvites, setPendingInvites] = useState<PendingInvitation[]>([]);
  const [dataVersion, setDataVersion] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { teamId, forceRefresh: teamsForceRefresh } = useTeamsContext();
  const { userId } = useUserContext();

  const fetchInvites = useCallback(async () => {
    // disabling API calls per 2025 April 30th
    return;
    try {
      const response = await fetch(`/api/teams/invite`);
      const data = await response.json();
      setPendingInvites(data);
    } catch (error) {
      setError(error);
    }

    setIsLoading(false);
  }, [userId, dataVersion]);

  useEffect(() => {
    fetchInvites();
    const interval = setInterval(fetchInvites, 15000);
    return () => clearInterval(interval);
  }, [fetchInvites]);

  const forceRefresh = () => {
    setDataVersion((prev) => prev + 1); // Increment dataVersion to trigger new fetch
  };

  const inviteUser = async (email: string) => {
    const result = await inviteUserApi(teamId, email);
    forceRefresh();
    teamsForceRefresh();
    return result;
  };

  const acceptInvite = async (teamId: string) => {
    await acceptInviteApi(teamId);
    forceRefresh();
    teamsForceRefresh();
  };

  const declineInvite = async (teamId: string) => {
    await declineInviteApi(teamId);
    forceRefresh();
  };

  const applyInviteCode = async (inviteCode: string) => {
    const result = await applyInviteCodeApi(inviteCode);
    forceRefresh();
    teamsForceRefresh();
    return result;
  };

  const value: IInvitesContext = {
    isLoading,
    forceRefresh,
    pendingInvites,
    acceptInvite,
    declineInvite,
    inviteUser,
    applyInviteCode,
    error
  };

  return (
    <InvitesContext.Provider value={value}>{children}</InvitesContext.Provider>
  );
};

export const useInvitesContext = () => {
  const context = useContext(InvitesContext);
  if (context === undefined) {
    throw new Error(
      'useInvitesContext must be used within a InviteContextProvider'
    );
  }
  return context;
};
