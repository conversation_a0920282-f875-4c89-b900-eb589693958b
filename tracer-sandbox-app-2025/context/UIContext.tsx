import React, { useCallback, useMemo, useReducer, Reducer } from 'react';

interface State {
  displayToast: boolean;
  toastTitle: string;
  toastContent: string;
  toastDuration: number;
  hasError: boolean;
}

interface Action {
  type: string;
  payload?: {
    toastTitle: string;
    toastContent: string;
    toastDuration?: number;
    hasError?: boolean;
  };
}

const initialState: State = {
  displayToast: false,
  toastTitle: 'This feature is not yet available in this version of the app',
  toastContent:
    'We are working hard on new features and will release more soon!',
  toastDuration: 10000, // default duration
  hasError: false
};

const uiReducer: Reducer<State, Action> = (state, action) => {
  switch (action.type) {
    case 'OPEN_TOAST': {
      const {
        toastTitle,
        toastContent,
        toastDuration = state.toastDuration,
        hasError = false
      } = action.payload!;
      console.log(
        '[UIContext.tsx] Opening toast',
        toastTitle,
        toastContent,
        toastDuration
      );
      return {
        ...state,
        displayToast: true,
        toastContent,
        toastTitle,
        toastDuration,
        hasError
      };
    }
    case 'CLOSE_TOAST': {
      return {
        ...state,
        displayToast: false
      };
    }
    default:
      return state;
  }
};

export const UIContext = React.createContext<{
  state: State;
  controls: {
    openToast: (
      title: string,
      content: string,
      duration?: number,
      hasError?: boolean
    ) => void;
    closeToast: () => void;
  };
}>({
  state: initialState,
  controls: { openToast: () => {}, closeToast: () => {} }
});

UIContext.displayName = 'UIContext';

export const UIProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const [state, dispatch] = useReducer(uiReducer, initialState);

  const openToast = useCallback(
    (
      toastTitle: string,
      toastContent: string,
      toastDuration?: number,
      hasError?: boolean
    ) =>
      dispatch({
        type: 'OPEN_TOAST',
        payload: {
          toastTitle,
          toastContent,
          toastDuration,
          hasError
        }
      }),
    [dispatch]
  );
  const closeToast = useCallback(
    () => dispatch({ type: 'CLOSE_TOAST' }),
    [dispatch]
  );

  const value = useMemo(
    () => ({
      state,
      controls: {
        openToast,
        closeToast
      }
    }),
    [state, openToast, closeToast]
  );

  return <UIContext.Provider value={value}>{children}</UIContext.Provider>;
};

export const useUI = () => {
  const context = React.useContext(UIContext);
  if (!context) {
    throw new Error(`useUI must be used within a UIProvider`);
  }
  return context;
};
