import { useState } from 'react';
import { ShadButton } from '@/components/ui/ShadButton';
import { createGrafanaUser, checkUserExists } from '../../../lib/grafanaService';
import { StepProps } from '../shared/types';

export const CredentialsStep: React.FC<StepProps> = ({ isActive, email, setError, setResponse, onComplete }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  const generateCredentials = async () => {
    setIsGenerating(true);
    setError(null);
    try {
      const existingUser = await checkUserExists(email);
      if (existingUser) {
        setResponse({ success: true, message: 'You are already registered in Grafana!' });
        setIsCompleted(true);
        onComplete();
        return;
      }
      const data = await createGrafanaUser({ email, name: email.split('@')[0], role: 'Viewer' });
      if (data.success) {
        setResponse({ success: true, message: 'Check your inbox for your Grafana credentials!' });
        setIsCompleted(true);
        onComplete();
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to generate credentials';
      setError(message);
      setResponse({ success: false, message });
    } finally {
      setIsGenerating(false);
    }
  };

  return isActive ? (
    <>
      {
        isCompleted ? (
          <div className="text-white text-sm mb-4">
            <div className="flex flex-col space-y-3">
              <div className="flex items-start">
                <span className="inline-block w-5 mr-2 text-right">1.</span>
                <span>Check your email to retrieve your Grafana credentials and then come back to continue</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-white text-sm mb-4">
            <div className="flex flex-col space-y-3">
              <div className="flex items-start">
                <span>Will be sent to: <span className="font-mono">{email}</span></span>
              </div>
            </div>
          </div>
        )
      }
      {!isCompleted && (
        <ShadButton
          onClick={generateCredentials}
          disabled={isGenerating}
          className="transition-transform duration-200 hover:scale-105 hover:shadow-lg"
        >
          {isGenerating ? 'Generating...' : 'Send Grafana Credentials'}
          <img
            src="https://assets.streamlinehq.com/image/private/w_300,h_300,ar_1/f_auto/v1/icons/3/grafana-ipeuuhi0ws3fbikv7ojrk.png/grafana-625ktq51icaz2carazn0n.png?_a=DATAdtAAZAA0"
            alt="Grafana Logo"
            className="ml-2 w-6 h-6"
          />
        </ShadButton>
      )}
    </>
  ) : null;
};
