export type Step = 'install' | 'run' | 'credentials' | 'checkEmail' | 'complete';

export interface StepProps {
  isActive: boolean;
  isCompleted: boolean;
  email: string;
  username: string;
  onComplete: () => void;
  setError: (error: string | null) => void;
  setResponse: (data: any) => void;
  showConfetti?: boolean;
}

export interface StepConfig {
  title: string;
  component: React.FC<StepProps>;
}
