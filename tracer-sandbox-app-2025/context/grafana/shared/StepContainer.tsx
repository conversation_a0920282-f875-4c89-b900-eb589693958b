import { CheckCircle2 } from 'lucide-react';

export const StepContainer: React.FC<{
  title: string;
  isActive: boolean;
  isCompleted: boolean;
  children: React.ReactNode;
}> = ({ title, isActive, isCompleted, children }) => (
  <div className={`w-full max-w-4xl my-3 ${isCompleted ? 'py-2 px-4 bg-gray-800' : isActive ? 'p-4 my-3 bg-gray-800' : 'p-3 bg-gray-900 opacity-50'} rounded-lg`}>
    <h2 className={`text-white ${isCompleted ? 'text-md' : 'text-lg'} mb-2 flex items-center`}>
      {title}
      {isCompleted && <CheckCircle2 className="ml-2 text-green-400 h-5 w-5" />}
    </h2>
    {isActive && children}
  </div>
);
