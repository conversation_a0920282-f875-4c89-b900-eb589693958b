import { IRun } from 'pages/api/db-queries/services-runs/fetchAllRunsForPipeline';
import { IService } from 'pages/api/db-queries/services-runs/getServices';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState
} from 'react';
import { useTeamsContext } from '../TeamsContext';

// Fetch services API with cache busting using a data version or timestamp
export const fetchServicesApi = async (teamId, dataVersion) => {
  // disabling API calls per 2025 April 30th
  return { services: [] };
  try {
 
    const response = await fetch(
      `/api/services?teamId=${encodeURIComponent(
        teamId
      )}&version=${dataVersion}`, // Include the version to ensure fresh data on each request
      {
        method: 'GET'
      }
    );
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data = await response.json();
    const { services } = data;

    return {
      services
    };
  } catch (error) {
    return { error: error instanceof Error ? error.message : String(error) };
  }
};

export interface IServicesContext {
  services: IService[];
  filteredServices: IService[];
  isLoading: boolean;
  forceRefresh: () => void;
  searchFilter: string;
  setSearchFilter: (filter: string) => void;
  lastRefreshed: number;
  error: string | null;
  activeService: string | null;
  activeServiceData: IService | null;
  setActiveService: (serviceName: string | null) => void;
  activeRun: string | null;
  setActiveRun?: (runName: string | null) => void;
  runs: IRun[];
  setRuns: (runs: IRun[]) => void;
  allRuns: { [key: string]: IRun[] };
  activeApiKey: string | null;
  setActiveApiKey?: (apiKey: string | null) => void;
}

const ServicesContext = createContext<IServicesContext | undefined>(undefined);

export const ServicesContextProvider = ({
  children
}: {
  children: React.ReactNode;
}) => {
  const [activeApiKey, setActiveApiKey] = useState<string | null>(null);
  const { teamId } = useTeamsContext();
  const [dataVersion, setDataVersion] = useState(0);
  const [services, setServices] = useState<IService[]>([]);
  const [allRuns, setAllRuns] = useState<{ [key: string]: IRun[] }>({});
  const [runs, setRuns] = useState<IRun[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchFilter, setSearchFilter] = useState('');
  const [lastRefreshed, setLastRefreshed] = useState(Date.now());
  const [error, setError] = useState<string | null>(null);
  const [activeService, setActiveService] = useState<string | null>(null);
  const [activeRun, setActiveRun] = useState<string | null>(null);

  const fetchServices = useCallback(async () => {
    if (!teamId) return;
    setIsLoading(true);
    setError(null);
    const { services, error } = await fetchServicesApi(teamId, dataVersion);

    setLastRefreshed(Date.now());

    if (services) {
      setServices(services);
    } else if (error) {
      setError(typeof error === 'string' ? error : JSON.stringify(error));
    }
    setIsLoading(false);
  }, [teamId, dataVersion]); // dependency on dataVersion

  useEffect(() => {
    if (!activeService) return;
    const service = services.find(
      (service) => service.serviceName === activeService
    );
    const serviceRuns = service?.runs || [];
    setRuns(serviceRuns);
  }, [activeService, setRuns, services, teamId]);

  useEffect(() => {
    const newAllRuns = { ...allRuns };
    for (const service of services) {
      if (!newAllRuns[service.serviceName]) {
        newAllRuns[service.serviceName] = service.runs;
      }
    }
    setAllRuns(newAllRuns);
  }, [services]);

  useEffect(() => {
    fetchServices(); // Initial fetch
    const interval = setInterval(fetchServices, 4000);
    return () => clearInterval(interval);
  }, [fetchServices]);

  const filteredServices = services.filter((service) =>
    service.serviceName.toLowerCase().includes(searchFilter.toLowerCase())
  );

  const forceRefresh = () => {
    setDataVersion((prev) => prev + 1); // Increment dataVersion to trigger new fetch
  };

  const value: IServicesContext = {
    services,
    filteredServices,
    isLoading,
    searchFilter,
    setSearchFilter,
    lastRefreshed,
    forceRefresh,
    error,
    activeService,
    activeServiceData: services.find(
      (service) => service.serviceName === activeService
    ) || null,
    setActiveService: (name) => {
      setActiveService(name);
    },
    activeRun,
    setActiveRun,
    runs,
    setRuns,
    allRuns,
    activeApiKey,
    setActiveApiKey
  };

  return (
    <ServicesContext.Provider value={value}>
      {children}
    </ServicesContext.Provider>
  );
};

export const useServicesContext = () => {
  const context = useContext(ServicesContext);
  if (context === undefined) {
    throw new Error(
      'useServicesContext must be used within a ServicesContextProvider'
    );
  }
  return context;
};
