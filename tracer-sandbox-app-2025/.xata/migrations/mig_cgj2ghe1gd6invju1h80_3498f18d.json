{"id": "mig_cgj2ghe1gd6invju1h80", "checksum": "1:3498f18d407e077eb137352597245109c900ef23f44d0d3451fa0a2cb85153ed", "operations": [{"addTable": {"table": "Posts"}}, {"addTable": {"table": "Users"}}, {"addColumn": {"column": {"name": "title", "type": "string"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "labels", "type": "multiple"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "slug", "type": "string"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "text", "type": "text"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "author", "type": "link", "link": {"table": "Users"}}, "table": "Posts"}}, {"addColumn": {"column": {"name": "createdAt", "type": "datetime"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "views", "type": "int"}, "table": "Posts"}}, {"addColumn": {"column": {"name": "name", "type": "string"}, "table": "Users"}}, {"addColumn": {"column": {"name": "email", "type": "email"}, "table": "Users"}}, {"addColumn": {"column": {"name": "bio", "type": "text"}, "table": "Users"}}]}