# Tracer Web App
This application will be used for users to guide them through the tracer installation process and automatically spin up Grafana accounts for them and retrieve those credentials.

## Goals May 2025 -
- Auto-create a Grafana organization and user, and retrieve credentials.
- Build a form to collect user input for Tracer installation.
- Validate that dashboards support the orgId URL parameter for routing.

**Test Criteria**
- Create a Grafana organization and user (simulate credential creation).
- Ensure orgId is dynamically used in dashboard URLs (e.g., orgId=x instead of orgId=1).
- Confirm dashboards load correctly using the orgId param, similar to pipeline_name.


## How to install and run this:
- Follow the installation instructions. 
- Ask Vincent for the .env file 

1. Install all npm packages 
```bash
pnpm install 
```

2. Run the app 
```bash
pnpm run dev
```


# Todo's 
- Update to NextJS V15
- Update all packages. 

# Approach V1: Querying by UserId and using templating
- We will have to do templating of the queries and adjust them per user_id
- This will need user_id in the queries 
- We set the column org_id in our database to Grafana org_id 


```bash 
SELECT *
FROM logs
WHERE org_id = '$user'
ORDER BY timestamp DESC
```

# Approach V2: Using org Id 
