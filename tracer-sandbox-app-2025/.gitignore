# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn
# next
.next

# testing
/coverage

# sanity
/studio/node_modules

# production
/build

# Sanity
/reactors-content


# yarn list
.yarn/*
!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

.env
*.env
*/env
.env.*
*/env.*
.env.local
*/env.local
.env.development
*/env.development
.env.test
*/env.test  

npm-debug.log*
yarn-debug.log*
yarn-error.log*

#amplify-do-not-edit-begin
amplify/\#current-cloud-backend
amplify/.config/local-*
amplify/logs
amplify/mock-data
amplify/mock-api-resources
amplify/backend/amplify-meta.json
amplify/backend/.temp
build/
dist/
node_modules/
aws-exports.js
awsconfiguration.json
amplifyconfiguration.json
amplifyconfiguration.dart
amplify-build-config.json
amplify-gradle-config.json
amplifytools.xcconfig
.secret-*
**.sample
#amplify-do-not-edit-end
