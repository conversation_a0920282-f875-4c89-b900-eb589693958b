import { NextApiRequest, NextApiResponse } from 'next';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_OK
} from '../constants/http-status';

const GRAFANA_API_URL = 'https://tracerbio.grafana.net/api';
const GRAFANA_TOKEN = process.env.GRAFANA_TOKEN!;

interface GrafanaUserRequest {
  email: string;
  name: string;
  role?: 'Viewer' | 'Editor' | 'Admin';
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(HTTP_BAD_REQUEST).json({
      success: false,
      error: `Method ${req.method} Not Allowed`
    });
  }

  try {
    const { email, name, role = 'Viewer' } = req.body as GrafanaUserRequest;

    if (!email || !name) {
      return res.status(HTTP_BAD_REQUEST).json({
        success: false,
        error: 'Email and name are required',
      });
    }

    // Extract username from email
    const username = email.split('@')[0];

    // 1. Create user, invite via email
    const inviteRes = await fetch(`${GRAFANA_API_URL}/org/invites`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${GRAFANA_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        loginOrEmail: email,
        name,
        role,
        sendEmail: true
      }),
    });

    if (!inviteRes.ok) {
      const err = await inviteRes.json().catch(() => ({}));
      
      // Check if the error is due to user already existing
      if (inviteRes.status === 409) {
        return res.status(409).json({
          success: false,
          message: 'You are already registered in Grafana!'
        });
      }
      
      return res.status(HTTP_INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Failed to invite user',
        details: err
      });
    }

    // 2. Get organization info
    const orgRes = await fetch(`${GRAFANA_API_URL}/org`, {
      headers: {
        Authorization: `Bearer ${GRAFANA_TOKEN}`,
        'Content-Type': 'application/json',
      },
    });

    if (!orgRes.ok) {
      throw new Error('Failed to get organization info');
    }

    const orgData = await orgRes.json();

    // 3. OK
    return res.status(HTTP_OK).json({
      success: true,
      message: 'User invited successfully',
      data: {
        email,
        name,
        username,
        role,
        organization: orgData.name,
        // dashboardUrl: `https://micverriello.grafana.net/dashboards?orgId=${orgData.id}`
      }
    });
  } catch (error) {
    return res.status(HTTP_INTERNAL_SERVER_ERROR).json({
      success: false,
      error: 'Unexpected server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
