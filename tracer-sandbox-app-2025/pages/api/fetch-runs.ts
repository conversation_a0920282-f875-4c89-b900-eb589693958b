import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import { HTTP_UNAUTHORIZED } from './constants/http-status';
import { getTeamByIdMember } from './db-queries/getTeams';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_NOT_ALLOWED,
  HTTP_OK
} from './db-queries/httpStatusCodes';
import {
  IRun,
  fetchAllRunsForPipeline
} from './db-queries/services-runs/fetchAllRunsForPipeline';

// Enhanced sanitizeInput function
function sanitizeInput(input: string): string {
  if (typeof input !== 'string' || !input.match(/^[a-zA-Z0-9\-_]+$/)) {
    throw new Error('Invalid serviceName format');
  }
  return input.trim();
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { serviceName, teamId } = req.query;

  if (!serviceName || typeof serviceName !== 'string') {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'serviceName is required and must be a string' });
  }

  if (!teamId || typeof teamId !== 'string') {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'teamId is required and must be a string' });
  }

  try {
    const sanitizedServiceName = sanitizeInput(serviceName);
    const user = getAuth(req);
    switch (req.method) {
      case 'GET':
        if (!user) {
          return res
            .status(HTTP_UNAUTHORIZED)
            .json({ error: 'User not found. Please sign in.' });
        }

        const team = await getTeamByIdMember({
          teamId, 
          memberUserId: user.userId}
        );

        if (!team) {
          return res.status(HTTP_BAD_REQUEST).json({ error: 'Team not found' });
        }

        const runs: IRun[] = await fetchAllRunsForPipeline(
          { name: sanitizedServiceName, teamId },
          true
        );
        res.status(HTTP_OK).json(runs);
        break;
      default:
        res.status(HTTP_NOT_ALLOWED).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('[api/fetchRuns.ts] Error:', error.message);
    res
      .status(HTTP_INTERNAL_SERVER_ERROR)
      .json({ error: `[api/fetchRuns.ts] Error: ${error.message}` });
  }
}
