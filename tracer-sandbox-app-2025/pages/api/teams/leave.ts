// api/teams/leave.ts
import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  getTeamByIdMember,
  removeTeamMember
} from '../db-queries/getTeams';
import {
  HTTP_BAD_REQUEST,
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_OK
} from '../db-queries/httpStatusCodes';

const postHandler = async (req: NextApiRequest, res: NextApiResponse) => {
  const user = getAuth(req);

  if (!user || !user.userId) {
    return res
      .status(HTTP_BAD_REQUEST)
      .json({ error: 'User is not authenticated' });
  }

  const teamId = req.body.teamId;

  if (!teamId || typeof teamId !== 'string' || teamId.trim().length === 0) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'teamId is required' });
  }

  const team = await getTeamByIdMember({ memberUserId: user.userId, teamId });

  if (!team) {
    return res.status(HTTP_BAD_REQUEST).json({ error: 'Team not found' });
  }

  await removeTeamMember(user.userId, teamId);

  return res.status(HTTP_OK).json({ success: true });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    switch (req.method) {
      case 'POST':
        return postHandler(req, res);
      default:
        return res
          .status(HTTP_BAD_REQUEST)
          .json({ error: 'Invalid request method' });
    }
  } catch (error) {
    console.error('[api/teams/remove.ts] Error retrieving tools details:', error);
    res.status(HTTP_INTERNAL_SERVER_ERROR).json({
      error: '[api/teams/remove.ts] Failed to retrieve tool details'
    });
  }
}
