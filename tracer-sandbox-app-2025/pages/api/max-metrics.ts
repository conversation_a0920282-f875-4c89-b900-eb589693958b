import { NextApiRequest, NextApiResponse } from 'next';
import { fetchMaxMetricsAgg } from './db-queries/fetchMaxMetricsAgg';
import {
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_NOT_ALLOWED,
  HTTP_OK
} from './db-queries/httpStatusCodes';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const serviceName = req.query.serviceName as string;

  if (!serviceName) {
    return res
      .status(HTTP_INTERNAL_SERVER_ERROR)
      .json({ error: 'serviceName is required' });
  }

  switch (req.method) {
    case 'GET':
      try {
        const page = await fetchMaxMetricsAgg([]);

        res.status(HTTP_OK).json(page);
      } catch (error) {
        console.error(
          '[api/max-metrics.ts] Error retrieving max-metrics:',
          error
        );
        res
          .status(HTTP_INTERNAL_SERVER_ERROR)
          .json({ error: '[api/fetchRuns.ts] Failed to retrieve services' });
      }
      break;
    default:
      res.status(HTTP_NOT_ALLOWED).end(`Method ${req.method} Not Allowed`);
  }
}
