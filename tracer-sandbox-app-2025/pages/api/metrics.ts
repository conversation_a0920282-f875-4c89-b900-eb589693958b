import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import { getMetrics } from './db-queries/getMetrics';
import { getTeamByIdMember } from './db-queries/getTeams';
import {
  HTTP_INTERNAL_SERVER_ERROR,
  HTTP_NOT_ALLOWED,
  HTTP_OK
} from './db-queries/httpStatusCodes';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { serviceName, startTimeUnix, endTimeUnix, teamId } = req.body;

  if (!serviceName || typeof serviceName !== 'string') {
    return res
      .status(HTTP_INTERNAL_SERVER_ERROR)
      .json({ error: 'serviceName is required' });
  }

  if (!teamId || typeof teamId !== 'string') {
    return res
      .status(HTTP_INTERNAL_SERVER_ERROR)
      .json({ error: 'teamId is required' });
  }

  switch (req.method) {
    case 'POST':
      const user = getAuth(req);

      if (!user) {
        return res
          .status(HTTP_INTERNAL_SERVER_ERROR)
          .json({ error: 'User is not authenticated' });
      }

      const team = await getTeamByIdMember({
        teamId,
        memberUserId: user.userId
      });

      if (!team) {
        return res
          .status(HTTP_INTERNAL_SERVER_ERROR)
          .json({ error: 'Team not found' });
      }

      try {
        const page = await getMetrics(
          { name: serviceName, teamId },
          Number(startTimeUnix),
          Number(endTimeUnix)
        );

        res.status(HTTP_OK).json(page);
      } catch (error) {
        console.error(
          '[api/max-metrics.ts] Error retrieving max-metrics:',
          error
        );
        res
          .status(HTTP_INTERNAL_SERVER_ERROR)
          .json({ error: '[api/fetchRuns.ts] Failed to retrieve services' });
      }
      break;
    default:
      res.status(HTTP_NOT_ALLOWED).end(`Method ${req.method} Not Allowed`);
  }
}
