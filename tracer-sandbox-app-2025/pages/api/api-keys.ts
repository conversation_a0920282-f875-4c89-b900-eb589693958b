import { clerkClient, getAuth } from '@clerk/nextjs/server';
import { customAlphabet } from 'nanoid';
import type { NextApiRequest, NextApiResponse } from 'next';
import { clickhouse, getAll, getFirst, query } from 'src/clickhouse';
import { ServiceId } from 'src/types';
import { AsyncLock } from 'src/utils';
import { ensureExistanceOfDefaultTeam, getDefaultTeamId, getTeamByIdMember } from './db-queries/getTeams';

type ApiKey = {
  apiKey: string;
  serviceName: string;
  userId: string;
  isActive: boolean;
  'xata.createdAt'?: string;
  'xata.updatedAt'?: string;
  msg?: string;
};

interface SuccessResponse {
  message: string;
  apiKeys?: ApiKey[];
}

interface ErrorResponse {
  error: string;
}

type ApiResponse = SuccessResponse | ErrorResponse;

const locks: { [user: string]: AsyncLock } = {};

const apiKeyAlphabet = customAlphabet(
  'abcdefghijklmnoprqstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789_',
  21
);

async function createApiKey(
  serviceId: ServiceId,
  username: string,
  userImgUrl: string
): Promise<ApiKey> {
  const apiKey = apiKeyAlphabet();
  const lock = new AsyncLock();
  lock.enable();
  const currentLock = locks[serviceId.teamId];
  if (currentLock) {
    await currentLock.promise;
  }
  locks[serviceId.teamId] = lock;
  const existingApiKey = await getFirst(
    `SELECT * FROM ApiKeys WHERE userId = {teamId: String} AND serviceName = {serviceName: String}`,
    {
      serviceName: serviceId.name,
      teamId: serviceId.teamId
    }
  );

  if (existingApiKey) {
    console.log('[api-keys] API key already exists for this service');
    delete locks[serviceId.teamId];
    lock.disable();
    return {
      ...existingApiKey,
      msg: 'API key already exists for this service'
    };
  }

  await query(
    'INSERT INTO ApiKeys SELECT {id: String} as id, {xataCreatedAt: DateTime} AS "xata.createdAt", {xataUpdatedAt: DateTime} AS "xata.updatedAt", {xataVersion: Int64} as "xata.version", {teamId: String} AS userId, {serviceName: String} AS serviceName, {apiKey: String} AS apiKey, {isActive: UInt8} AS isActive, {typeEnvironment: String} as typeEnvironment, {username: String} AS username, {userImgUrl: String} AS userImgUrl \
    FROM system.one WHERE NOT EXISTS (SELECT * FROM ApiKeys WHERE serviceName = {serviceName: String} AND userId = {teamId: String}) ',
    {
      id: '',
      serviceName: serviceId.name,
      teamId: serviceId.teamId,
      apiKey,
      username,
      userImgUrl,
      isActive: true,
      typeEnvironment: '',
      xataVersion: 0,
      xataCreatedAt: new Date(),
      xataUpdatedAt: new Date()
    }
  );

  delete locks[serviceId.teamId];
  lock.disable();

  return await getFirst(
    `SELECT * FROM ApiKeys WHERE userId = {teamId: String} AND serviceName = {serviceName: String}`,
    {
      serviceName: serviceId.name,
      teamId: serviceId.teamId
    }
  );
}

async function deleteApiKey(apiKey: string, teamId: string): Promise<void> {
  const keyToDelete = await getFirst(
    `SELECT * FROM ApiKeys WHERE apiKey = {apiKey: String} AND userId = {teamId: String}`,
    {
      apiKey,
      teamId
    }
  );
  if (!keyToDelete) {
    throw new Error('[api-keys] API key not found');
  }
  await clickhouse.command({
    query: `DELETE FROM ApiKeys WHERE apiKey = {apiKey: String} AND userId = {teamId: String}`,
    query_params: {
      apiKey,
      teamId
    }
  });
  return Promise.resolve();
}

async function listApiKeys(teamId: string): Promise<ApiKey[]> {
  const keys = await getAll(
    `SELECT * FROM ApiKeys WHERE userId = {teamId: String} ORDER BY xata.createdAt ASC`,
    {
      teamId
    }
  );

  const keysToDelete: string[] = [];

  const deduplicatedKeys = keys.reduce((acc: ApiKey[], key: ApiKey) => {
    const existingKey = acc.find((k) => k.serviceName === key.serviceName);
    if (!existingKey) {
      return [...acc, key];
    } else {
      keysToDelete.push(key.apiKey);
    }

    return acc;
  }, []);

  for (const key of keysToDelete) {
    await deleteApiKey(key, teamId);
  }

  return deduplicatedKeys;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const user = getAuth(req);

  try {
    if (req.method === 'POST') {
      const { serviceName, teamId } = req.body;
      if (
        !user ||
        !serviceName ||
        !teamId ||
        typeof serviceName !== 'string' ||
        typeof teamId !== 'string'
      )
        throw new Error('[api-keys] Missing required fields');

      const userBackendObject = await clerkClient.users.getUser(user.userId);

      if (!userBackendObject) {
        throw new Error('[api-keys] User not found');
      }

      if (teamId === getDefaultTeamId(user.userId)) {
        await ensureExistanceOfDefaultTeam(user.userId);
      } else {
        const team = await getTeamByIdMember({
          teamId,
          memberUserId: user.userId
        });

        if (!team) {
          throw new Error('[api-keys] Team not found');
        }
      }

      const apiKey = await createApiKey(
        {
          name: serviceName,
          teamId
        },
        userBackendObject.username,
        userBackendObject.imageUrl
      );

      return res
        .status(200)
        .json({ message: 'API key created successfully', apiKeys: [apiKey] });
    } else if (req.method === 'DELETE') {
      console.log('attempting deletion with body', req.body);
      const { apiKey, teamId } = req.body;
      if (!apiKey || !user || !teamId)
        throw new Error('[api-keys] Missing apiKey for deletion');

      const team = await getTeamByIdMember({
        teamId,
        memberUserId: user.userId
      });

      if (!team) {
        throw new Error('[api-keys] Team not found');
      }

      await deleteApiKey(apiKey, teamId);
      return res.status(200).json({ message: 'API key deleted successfully' });
    } else if (req.method === 'GET') {
      const { teamId } = req.query;
      if (!teamId || typeof teamId !== 'string')
        throw new Error('[api-keys] Missing teamId for listing');

      const team = await getTeamByIdMember({
        teamId,
        memberUserId: user.userId
      });

      if (!team) {
        throw new Error('[api-keys] Team not found');
      }

      const apiKeys = await listApiKeys(teamId);
      return res
        .status(200)
        .json({ message: `API keys listed successfully`, apiKeys });
    } else {
      res.setHeader('Allow', ['POST', 'DELETE', 'GET']);
      return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    return res.status(500).json({
      error:
        error instanceof Error ? error.message : 'An unexpected error occurred'
    });
  }
}
