import { getFirst } from 'src/clickhouse';
import { RUN_STATUS_ONGOING } from '../constants/pipeline-status';
import { IRun } from './services-runs/fetchAllRunsForPipeline';

export async function fetchMaxMetricsAgg(runs: IRun[]): Promise<IRun[]> {
  await new Promise((resolve) => setTimeout(resolve, 50)); // Initial delay to avoid hitting rate limit

  for (const run of runs) {
    if (run.run_end_definition_type === RUN_STATUS_ONGOING) {
      // If the run is ongoing, set the end date to now otherwise the aggregation will fail
      run.end_unixDate = Math.floor(Date.now());
    } else if (!run.run_name || typeof run.end_unixDate !== 'number') {
      console.warn(
        'Invalid run data: each run must have a valid name, start_unixDate, and end_unixDate.'
      );
      return runs;
    }
  }

  const results = await Promise.all(
    runs.map(async (run) => {
      try {
        const metricAggregations = {
          aggs: await getFirst(
            'SELECT max(system_cpu_utilization) as maxCpu, min(system_cpu_utilization) as minCpu, \
          max(system_disk_utilization) as maxDiskUtilizationPercent, max(system_memory_utilization) as maxMemUsedPercent, \
          max(system_memory_used) as maxMemUsed, 0 as networkTrafficReceivedSumBytes, 0 as networkTrafficSentSumBytes, \
          0 as diskReadSizeMaxBytes, 0 as diskWriteSizeMaxBytes FROM logs WHERE timestamp >= {start_unixDate: Int64} \
          AND timestamp <= {end_unixDate: Int64} AND run_id = {run_id: String}',
            {
              run_name: run.run_name,
              start_unixDate: run.start_unixDate,
              end_unixDate: run.end_unixDate,
              run_id: run.run_id
            }
          )
        };
        return { ...run, ...metricAggregations }; // Embed the results into the run object
      } catch (error) {
        console.error(`Error processing run ${run.run_name}:`, error);
        return { ...run, aggs: null }; // Handle error by assigning null results
      }
    })
  );

  return results; // Return the updated array of runs with embedded results
}
