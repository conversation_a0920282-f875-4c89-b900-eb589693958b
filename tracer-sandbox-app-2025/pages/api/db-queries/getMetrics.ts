import { getAll } from 'src/clickhouse';
import { ServiceId } from 'src/types';

export async function getMetrics(
  serviceId: ServiceId,
  startTimeUnix: number,
  endTimeUnix: number
) {
  const result = await getAll(
    `SELECT * FROM logs WHERE timestamp >= {startTimeUnix: UInt64} AND timestamp <= {endTimeUnix: UInt64} AND service_name = {serviceName: String} AND user_id = {teamId: String} AND record_type IN ({recordTypes: Array(String)})`,
    {
      startTimeUnix,
      endTimeUnix,
      serviceName: serviceId.name,
      teamId: serviceId.teamId,
      recordTypes: ['metric_event']
    }
  );

  return result;
}
