import dayjs from 'dayjs';
import { getAll } from 'src/clickhouse';
import { ServiceId } from 'src/types';
import {
  PROCESS_STATUS_METRIC_EVENT,
  PROCESS_STATUS_TOOL_METRIC_EVENT
} from '../constants/pipeline-status';
import {
  TOOL_FINISHED_RECORD_TYPE,
  TOOL_RECORD_TYPE
} from '../constants/record-types';
import { calculateDuration } from './services-runs/fetchAllRunsForPipeline';

async function getToolEvents(
  serviceId: ServiceId,
  start_unixDate: number,
  end_unixDate: number
) {
  false &&
    console.log(
      'Trying to fetch tool events for time range',
      {
        start_unixDate,
        end_unixDate
      },
      'which is',
      new Date(start_unixDate),
      'to',
      new Date(end_unixDate)
    );

  const toolEvents = await getAll(
    'SELECT * FROM logs WHERE timestamp >= {start_unixDate: Int64} \
      AND timestamp <= {end_unixDate: Int64} \
      AND service_name = {serviceName: String} \
      AND user_id = {teamId: String} \
      AND record_type IN ({recordType: Array(String)})',
    {
      start_unixDate: Number(start_unixDate),
      end_unixDate: Number(end_unixDate),
      serviceName: serviceId.name,
      teamId: serviceId.teamId,
      recordType: [
        TOOL_RECORD_TYPE,
        TOOL_FINISHED_RECORD_TYPE,
        PROCESS_STATUS_TOOL_METRIC_EVENT
      ]
    }
  );

  return toolEvents;
}

export async function consolidateToolEvents(
  toolRecords: any[],
  end_unixDate: number
) {
  const toolStartEvents = toolRecords.filter(
    (tool: any) => tool.record_type === TOOL_RECORD_TYPE
  );

  const toolEndEvents = toolRecords.filter(
    (tool: any) => tool.record_type === TOOL_FINISHED_RECORD_TYPE
  );

  const toolMetricEvents = toolRecords.filter(
    (tool: any) => tool.record_type === PROCESS_STATUS_TOOL_METRIC_EVENT
  );

  const toolEvents = toolStartEvents.map((tool: any, index: any) => {
    const nextTool = toolRecords[index + 1];
    const isLastRun = index === toolRecords.length - 1;
    const tool_start_unixDate = Number(tool.timestamp);
    const pid = tool.properties.attributes.tool_pid;
    const parentPid = tool?.properties?.attributes?.tool_parent_pid;
    const endEvent = toolEndEvents.find(
      (x) => x.properties.attributes.tool_pid === pid
    );
    const metricEvents = toolMetricEvents.filter(
      (x) => x.properties.attributes.tool_pid === pid
    );
    const tool_end_unixDate =
      endEvent?.timestamp ||
      (isLastRun ? end_unixDate : Number(nextTool?.timestamp)) ||
      undefined;
    const duration =
      endEvent?.properties?.attributes?.duration / 1000 ||
      calculateDuration(tool_start_unixDate, tool_end_unixDate);
    return {
      ...tool,
      toolName: tool?.properties?.attributes?.tool_name,
      toolCmd: tool?.properties?.attributes?.tool_cmd,
      toolPid: tool?.properties?.attributes?.tool_pid,
      mainToolPid: tool?.properties?.attributes?.tool_pid,
      toolStatus: endEvent
        ? 'Finished'
        : metricEvents
            .reverse()
            .find((x) => x?.properties?.attributes?.tool_pid === pid)
            ?.properties?.tool_process_status || 'Unknown',
      parentPid,
      start_unixDate: tool_start_unixDate,
      end_unixDate: tool_end_unixDate,
      timestamp: tool?.properties?.attributes?.start_timestamp,
      duration
    };
  });

  // Merge tools with the same cmd and the same parent pid
  const mergedToolEvents = toolEvents.reduce((acc, tool) => {
    const existingTool = acc.find(
      (x) =>
        (x.toolCmd === tool.toolCmd && x.parentPid === tool.parentPid) ||
        x.mainToolPid === tool.parentPid ||
        x.parentPid === tool.mainToolPid
    );
    if (existingTool) {
      if (
        existingTool.parentPid !== tool.mainToolPid &&
        existingTool.mainToolPid !== tool.parentPid
      ) {
        existingTool.toolPid = `${existingTool.toolPid}, ${tool.toolPid}`;
      }

      existingTool.end_unixDate = Math.max(
        tool.end_unixDate,
        existingTool.end_unixDate
      );

      existingTool.start_unixDate = Math.min(
        tool.start_unixDate,
        existingTool.start_unixDate
      );

      if (
        new Date(existingTool.timestamp) > new Date(tool.timestamp) ||
        existingTool.parentPid === tool.mainToolPid
      ) {
        existingTool.mainToolPid = tool.mainToolPid;
        existingTool.properties = tool.properties;
        existingTool.timestamp = tool.timestamp;
        existingTool.message = tool.message;
        existingTool.toolPid = tool.toolPid;
        existingTool.parentPid = tool.parentPid;
        existingTool.toolStatus = tool.toolStatus;
      }

      existingTool.duration =
        (existingTool.end_unixDate - existingTool.start_unixDate) / 1000 ||
        Math.max(existingTool.duration, tool.duration);

      return acc;
    }
    return [...acc, tool];
  }, []);

  return mergedToolEvents;
}

export async function fetchToolsAnalytics(
  serviceId: ServiceId,
  start_unixDate,
  end_unixDate
) {
  await new Promise((resolve) => setTimeout(resolve, 100)); // Initial delay to avoid hitting rate limit

  let toolEvents = [];
  let toolRecords = [];
  try {
    toolRecords = await getToolEvents(serviceId, start_unixDate, end_unixDate);

    toolEvents = await consolidateToolEvents(toolRecords, end_unixDate);
  } catch (error) {
    console.error('Error fetching tool events:', error);
    return []; // Handle error by returning an empty array or appropriate default value
  }

  toolEvents.sort((a: any, b: any) =>
    dayjs(a.timestamp).diff(dayjs(b.timestamp))
  );

  const allStatistics = await getAll(
    'SELECT system_memory_total , \
          system_disk_utilization, \
          system_memory_swap_total, \
          timestamp \
          FROM logs WHERE timestamp >= {start_unixDate: Int64} \
          AND timestamp <= {end_unixDate: Int64} AND service_name = {serviceName: String} AND user_id = {teamId: String} AND record_type = {recordType: String} \
          ORDER BY timestamp ASC',
    {
      serviceName: serviceId.name,
      teamId: serviceId.teamId,
      start_unixDate: Number(start_unixDate),
      end_unixDate: Number(end_unixDate),
      recordType: PROCESS_STATUS_METRIC_EVENT
    }
  );
  const results = await Promise.all(
    toolEvents.map(async (tool) => {
      await new Promise((resolve) => setTimeout(resolve, 50));
      const secondaryToolPids = tool.toolPid.split(',').map((x) => x.trim());
      const thisToolRecords = toolRecords.filter(
        (x) =>
          secondaryToolPids.includes(x.properties.tool_pid) &&
          x.record_type === PROCESS_STATUS_TOOL_METRIC_EVENT
      );

      try {
        const systemAggsRecords = await getAll(
          'SELECT system_disk_utilization, \
          system_memory_swap_used, \
          timestamp \
          FROM logs WHERE timestamp >= {start_unixDate: Int64} \
          AND timestamp <= {end_unixDate: Int64} AND run_id = {run_id: String} \
          AND record_type = {recordType: String} ORDER BY timestamp ASC',
          {
            start_unixDate: Number(tool.start_unixDate),
            end_unixDate: Number(tool.end_unixDate),
            run_id: tool.run_id,
            recordType: PROCESS_STATUS_METRIC_EVENT
          }
        );

        const systemAggs = systemAggsRecords.reduce(
          (acc, x) => ({
            maxDiskUtilizationPercent: Math.max(
              acc.maxDiskUtilizationPercent || 0,
              x.system_disk_utilization
            ),
            maxSwapUsed: Math.max(
              acc.maxSwapUsed || 0,
              x.system_memory_swap_used
            )
          }),
          {}
        );

        const closest = allStatistics.reduce((acc, x) => {
          if (x.timestamp < tool.start_unixDate) {
            return x;
          }
          return acc;
        });

        const aggs = {
          maxCpu: thisToolRecords.reduce(
            (acc, x) => Math.max(acc || 0, x.process_cpu_utilization),
            null
          ),
          maxDiskUtilizationPercent:
            systemAggs.maxDiskUtilizationPercent ||
            closest.system_disk_utilization,
          maxMemUsedPercent:
            (thisToolRecords.reduce(
              (acc, x) => Math.max(acc || 0, x.process_memory_used),
              null
            ) /
              closest.system_memory_total) *
            100.0,
          maxSwapUsedPercent:
            (systemAggs.maxSwapUsed / closest.system_memory_swap_total) * 100.0,
          systemMetrics: systemAggsRecords.map((x) => ({
            swapUsedPercent:
              (x.system_memory_swap_used / closest.system_memory_swap_total) *
              100.0,
            timestamp: x.timestamp
          }))
        };
        return { ...tool, aggs }; // Embed the results into the run object
      } catch (error) {
        console.error(`[fetchToolsAnalytics] Error processing tool:`, error);
        return { ...tool, aggs: null }; // Handle error by assigning null results
      }
    })
  );

  return results; // Return the updated array of runs with embedded results
}

export const fetchToolDetails = async (
  serviceId: ServiceId,
  start_unixDate: number,
  end_unixDate: number,
  toolPid: string
) => {
  const events = await getAll(
    'SELECT * FROM logs WHERE timestamp >= {start_unixDate: Int64} \
      AND timestamp <= {end_unixDate: Int64} \
      AND service_name = {serviceName: String} \
      AND user_id = {teamId: String} \
      AND record_type IN ({recordType: Array(String)}) \
      AND tool_pid IN ({toolPid: Array(String)})',
    {
      start_unixDate: Number(start_unixDate),
      end_unixDate: Number(end_unixDate),
      serviceName: serviceId.name,
      teamId: serviceId.teamId,
      recordType: [PROCESS_STATUS_TOOL_METRIC_EVENT],
      toolPid: toolPid.split(',').map((x) => x.trim())
    }
  );

  return events;
};
