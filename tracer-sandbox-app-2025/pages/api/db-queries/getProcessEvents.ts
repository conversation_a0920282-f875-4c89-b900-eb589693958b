import dayjs from 'dayjs';
import { getAll } from 'src/clickhouse';
import { PROCESS_STATUS_TOOL_METRIC_EVENT } from '../constants/pipeline-status';
import {
  PROCESS_STATUS_RECORD_TYPE,
  TOOL_FINISHED_RECORD_TYPE,
  TOOL_RECORD_TYPE
} from '../constants/record-types';
import { consolidateToolEvents } from './fetchToolsAnalytics';

export async function getProcessEvents({
  start_unixDate,
  end_unixDate,
  serviceName,
  teamId
}: {
  start_unixDate: number;
  end_unixDate: number;
  serviceName: string;
  teamId: string;
}) {
  const events = await getAll(
    `SELECT * FROM logs WHERE timestamp >= {start_unixDate: UInt64} AND 
        timestamp < {end_unixDate: UInt64} AND service_name = {serviceName: String} AND 
        user_id = {teamId: String} AND record_type IN ({recordTypes: Array(String)}) 
    LIMIT 1000`,
    {
      start_unixDate,
      end_unixDate,
      serviceName,
      recordTypes: [
        PROCESS_STATUS_RECORD_TYPE,
        TOOL_RECORD_TYPE,
        TOOL_FINISHED_RECORD_TYPE
      ],
      teamId
    }
  );

  const toolEvents = await consolidateToolEvents(events, end_unixDate);

  const processRecords = events.filter(
    (event: any) =>
      event.record_type !== TOOL_RECORD_TYPE &&
      event.record_type !== TOOL_FINISHED_RECORD_TYPE &&
      event.record_type !== PROCESS_STATUS_TOOL_METRIC_EVENT
  );

  const records = [...processRecords, ...toolEvents];

  records.sort((a: any, b: any) => dayjs(a.timestamp).diff(dayjs(b.timestamp)));

  return { records };
}
