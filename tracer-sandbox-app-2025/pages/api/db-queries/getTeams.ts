import { clerkClient } from '@clerk/nextjs';
import dayjs from 'dayjs';
import { customAlphabet } from 'nanoid';
import { command, create, getAll, getFirst, query } from 'src/clickhouse';
import {
  ExternalInvitiation,
  PendingInvitation,
  Team,
  TeamMemberState
} from 'src/types';

export const getDefaultTeamId = (userId: string) => {
  return `${userId}`;
};

const USER_DATA_VALIDITY_MINUTES = 3 * 60;

const userDataCache: {
  [userId: string]: {
    username: string;
    imageUrl: string;
    email: string;
    validUntil: Date;
  };
} = {};

export const clearInvalidatedUserData = () => {
  const now = new Date();

  for (const userId in userDataCache) {
    if (userDataCache[userId].validUntil < now) {
      delete userDataCache[userId];
    }
  }
};

export const getUserData = async (userId: string) => {
  clearInvalidatedUserData();

  if (userId in userDataCache) {
    return userDataCache[userId];
  }

  const user = await clerkClient.users.getUser(userId);

  const email =
    user?.emailAddresses.find((x) => x.id === user?.primaryEmailAddressId)
      ?.emailAddress || '';

  const result = {
    username: user?.username || user?.lastName || email,
    imageUrl: user?.imageUrl || '',
    email,
    validUntil: dayjs().add(USER_DATA_VALIDITY_MINUTES, 'minutes').toDate()
  };

  userDataCache[userId] = result;

  return result;
};

export const getUserDataBundle = async (userIds: string[]) => {
  clearInvalidatedUserData();

  const missingUserIds = userIds.filter(
    (x) => !(x in userDataCache) || !userDataCache[x]
  );

  const deduplicatedMissingUserIds = Array.from(new Set(missingUserIds));

  const result: {
    [userId: string]: {
      username: string;
      imageUrl: string;
      email: string;
      validUntil: Date;
    };
  } = {};

  if (deduplicatedMissingUserIds.length !== 0) {
    const users = await clerkClient.users.getUserList({
      userId: deduplicatedMissingUserIds
    });

    for (const user of users) {
      if (!user) {
        console.log('Failed to get user data for user', user.id);
        continue;
      }

      const email =
        user.emailAddresses.find((x) => x.id === user.primaryEmailAddressId)
          ?.emailAddress || '';

      result[user.id] = {
        username: user.username || user.lastName || email || '',
        imageUrl: user.imageUrl || '',
        email,
        validUntil: dayjs().add(USER_DATA_VALIDITY_MINUTES, 'minutes').toDate()
      };

      userDataCache[user.id] = result[user.id];
    }
  }

  for (const userId of userIds) {
    if (!(userId in result && userId in userDataCache)) {
      result[userId] = userDataCache[userId];
    }
  }

  return result;
};

export const getTeams = async (userId: string): Promise<Team[]> => {
  const [ownTeams, otherTeams, sentExternalInvites] = await Promise.all([
    getAll(
      'SELECT * FROM Teams AS T LEFT JOIN TeamMembers AS TM ON T.id = TM.teamId WHERE T.ownerUserId = {userId: String}',
      {
        userId
      }
    ),
    getAll(
      "SELECT * FROM Teams AS T \
        LEFT JOIN TeamMembers AS TMOthers ON T.id = TMOthers.teamId \
        RIGHT JOIN TeamMembers AS TMUser ON T.id = TMUser.teamId \
        WHERE TMUser.userId = {userId: String} AND TMUser.state = 'joined'",
      {
        userId
      }
    ),
    getAll(
      'SELECT * FROM TeamExternalInvitations AS TEI LEFT JOIN Teams AS T ON T.id = TEI.teamId WHERE T.ownerUserId = {userId: String}',
      {
        userId
      }
    )
  ]);

  if (ownTeams.length === 0) {
    await create('Teams', {
      id: getDefaultTeamId(userId),
      name: 'Your team',
      ownerUserId: userId
    });

    return await getTeams(userId);
  }

  const users = await getUserDataBundle(
    [
      userId,
      ...ownTeams.filter((x) => x && x.userId !== '').map((x) => x.userId),
      ...otherTeams
        .filter((x) => x && x.userId !== '')
        .map((x) => x['TMOthers.userId']),
      ...otherTeams
        .filter((x) => x && x.userId !== '')
        .map((x) => x['T.ownerUserId'])
    ].filter((x) => x !== '')
  );

  const result: Team[] = [];

  const teams: { [id: string]: Team } = {};

  for (const team of ownTeams) {
    const user = users[team.userId || userId];
    if (team.id in teams && team.userId && user) {
      teams[team.id].members.push({
        userId: team.userId,
        userName: user.username,
        userAvatarUrl: user.imageUrl,
        email: user.email,
        state: team.state,
        owner: false
      });
    } else {
      const members =
        team.userId && user
          ? [
              {
                userId: team.userId,
                userName: user.username,
                userAvatarUrl: user.imageUrl,
                email: user.email,
                state: team.state,
                owner: false
              }
            ]
          : [];
      teams[team.id] = {
        id: team.id,
        name: team.name,
        members,
        ownerUserId: team.ownerUserId,
        createdAt: new Date(team.createdAt),
        type: 'owner',
        externalInvitations: sentExternalInvites.filter(
          (x) => x.teamId === team.id
        )
      };
    }
  }

  for (const team of otherTeams) {
    const otherUserId = team['TMOthers.userId'];
    const teamId = team['T.id'];

    const user = users[otherUserId];

    if (teamId in teams && user) {
      teams[teamId].members.push({
        userId: otherUserId,
        userName: user.username,
        userAvatarUrl: user.imageUrl,
        email: user.email,
        state: team['TMOthers.state'],
        owner: false
      });
    } else {
      teams[teamId] = {
        id: teamId,
        name: team['T.name'],
        members: user
          ? [
              {
                userId: otherUserId,
                userName: user.username,
                userAvatarUrl: user.imageUrl,
                email: user.email,
                state: team['TMOthers.state'],
                owner: false
              }
            ]
          : [],
        ownerUserId: team['T.ownerUserId'],
        createdAt: new Date(team['T.createdAt']),
        type: 'member',
        externalInvitations: []
      };
    }
  }

  for (const team of Object.values(teams)) {
    const user = users[team.ownerUserId];
    if (!user) {
      continue;
    }
    result.push({
      ...team,
      members: [
        {
          userId: team.ownerUserId,
          userName: user.username,
          userAvatarUrl: user.imageUrl,
          email: user.email,
          state: 'joined',
          owner: true
        },
        ...team.members
      ]
    });
  }

  result.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());

  return result;
};

export const addTeam = async ({
  userId,
  name
}: {
  userId: string;
  name: string;
}) => {
  await create('Teams', {
    name,
    ownerUserId: userId
  });
};

export const getTeam = async ({
  userId,
  teamId
}: {
  userId: string;
  teamId: string;
}): Promise<Team> => {
  const team = await getFirst(
    'SELECT * FROM Teams WHERE id = {teamId: String} AND ownerUserId = {userId: String}',
    {
      teamId,
      userId
    }
  );

  if (!team) {
    return null;
  }

  return team;
};

export const getTeamWithMembers = async (
  userId: string,
  teamId: string
): Promise<Team> => {
  const team = await getFirst(
    'SELECT * FROM Teams AS T JOIN TeamMembers AS TM ON T.id = TM.teamId WHERE T.id = {teamId: String} AND T.ownerUserId = {userId: String} ',
    {
      teamId,
      userId
    }
  );

  if (!team) {
    return null;
  }

  const members = [];

  for (const member of team) {
    members.push({
      userId: member.TM.userId,
      state: member.TM.state
    });
  }

  return {
    ...team,
    members
  };
};

export const getTeamByIdMember = async ({
  teamId,
  memberUserId
}: {
  teamId: string;
  memberUserId: string;
}): Promise<{ ownerUserId: string }> => {
  const team = await getFirst(
    "SELECT T.ownerUserId AS ownerUserId FROM Teams AS T JOIN TeamMembers AS TM ON T.id = TM.teamId WHERE T.id = {teamId: String} AND TM.userId = {memberUserId: String} AND TM.state = 'joined' UNION ALL SELECT T.ownerUserId AS ownerUserId FROM Teams AS T WHERE T.id = {teamId: String} AND T.ownerUserId = {memberUserId: String}",
    {
      teamId,
      memberUserId
    }
  );

  if (!team) {
    return null;
  }

  return {
    ownerUserId: team.ownerUserId
  };
};

export const ensureExistanceOfDefaultTeam = async (userId: string) => {
  const team = await getFirst(
    'SELECT * FROM Teams WHERE id = {teamId: String} AND ownerUserId = {userId: String}',
    {
      teamId: getDefaultTeamId(userId),
      userId
    }
  );

  if (!team) {
    await create('Teams', {
      id: getDefaultTeamId(userId),
      name: 'Personal',
      ownerUserId: userId
    });
  }
};

export const updateTeamName = async (
  userId: string,
  teamId: string,
  name: string
) => {
  await getAll(
    'ALTER TABLE Teams UPDATE name = {name: String} WHERE id = {teamId: String} AND ownerUserId = {userId: String}',
    {
      teamId,
      userId,
      name
    }
  );
};

export const deleteTeam = async (userId: string, teamId: string) => {
  await command('DELETE FROM logs WHERE user_id = {teamId: String}', {
    teamId
  });

  await command('DELETE FROM ApiKeys WHERE userId = {teamId: String}', {
    teamId
  });

  await command('DELETE FROM TeamMembers WHERE teamId = {teamId: String}', {
    teamId
  });

  await command(
    'DELETE FROM Teams WHERE id = {teamId: String} AND ownerUserId = {userId: String}',
    {
      teamId,
      userId
    }
  );
};

export const addTeamMember = async (
  userId: string,
  teamId: string,
  state: TeamMemberState
) => {
  await create('TeamMembers', {
    teamId,
    userId,
    state
  });
};

export const removeTeamMember = async (userId: string, teamId: string) => {
  await command(
    'DELETE FROM TeamMembers WHERE teamId = {teamId: String} AND userId = {userId: String}',
    {
      teamId,
      userId
    }
  );
};

export const acceptTeamInvitation = async (
  userId: string,
  teamId: string
): Promise<void> => {
  await getAll(
    'ALTER TABLE TeamMembers UPDATE state = {state: String} WHERE teamId = {teamId: String} AND userId = {userId: String}',
    {
      teamId,
      userId,
      state: 'joined'
    }
  );
};

export const declineTeamInvitation = async (
  userId: string,
  teamId: string
): Promise<void> => {
  await command(
    'DELETE FROM TeamMembers WHERE teamId = {teamId: String} AND userId = {userId: String}',
    {
      teamId,
      userId
    }
  );
};

export const getInvitations = async (userId: string) => {
  const invitations = await getAll(
    'SELECT * FROM TeamMembers AS TM JOIN Teams AS T ON TM.teamId = T.id WHERE TM.userId = {userId: String} AND TM.state = {state: String}',
    {
      userId,
      state: 'invited'
    }
  );

  const result: PendingInvitation[] = [];

  const users = await getUserDataBundle(invitations.map((x) => x.ownerUserId));

  for (const invitation of invitations) {
    const user = users[invitation.ownerUserId];

    if (!user) {
      continue;
    }

    result.push({
      teamId: invitation.id,
      userId: userId,
      teamName: invitation.name,
      teamUserAvatarUrl: user.imageUrl,
      teamUserName: user.username
    });
  }

  return result;
};

export const getExternalInvitations = async (
  teamId: string
): Promise<ExternalInvitiation[]> => {
  const teams = await getAll(
    'SELECT * FROM TeamExternalInvitations WHERE teamId = {teamId: String}',
    {
      teamId
    }
  );

  return teams;
};

export const getExternalInvitationByCode = async (
  code: string
): Promise<ExternalInvitiation> => {
  const invitation = await getFirst(
    'SELECT * FROM TeamExternalInvitations WHERE code = {code: String}',
    {
      code
    }
  );

  return invitation;
};

export const deleteExternalInvitation = async (code: string): Promise<void> => {
  await command(
    'DELETE FROM TeamExternalInvitations WHERE code = {code: String}',
    {
      code
    }
  );
};

const invitationCodeAlphabet = customAlphabet(
  'ABCDEFGHIJKLMNOPQRSTUWXYZ0123456789',
  8
);

export const createExternalInvitation = async ({
  email,
  teamId
}: {
  email: string;
  teamId: string;
}) => {
  const code = invitationCodeAlphabet();

  await create('TeamExternalInvitations', {
    teamId,
    code,
    email
  });

  return code;
};

export const prepareUsersDefaultApiKeys = async (userId: string) => {
  const apiKeys = await getAll(
    "SELECT * FROM ApiKeys WHERE userId = {userId: String} AND teamId = ''",
    { userId }
  );

  if (apiKeys.length === 0) {
    return;
  }

  const team = await getFirst(
    'SELECT * FROM Teams WHERE ownerUserId = {userId: String} AND teamId = {teamId: String}',
    { userId, teamId: getDefaultTeamId(userId) }
  );

  if (!team) {
    await create('Teams', {
      id: getDefaultTeamId(userId),
      name: 'Personal',
      ownerUserId: userId
    });
  }

  await query(
    "UPDATE ApiKeys SET teamId = {teamId: String} WHERE userId = {userId: String} AND teamId = ''",
    {
      userId,
      teamId: getDefaultTeamId(userId)
    }
  );
};
