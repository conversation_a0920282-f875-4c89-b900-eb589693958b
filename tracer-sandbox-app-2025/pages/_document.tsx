// pages/_document.ts
import Document, { Html, Head, Main, NextScript } from 'next/document';
import { ServerStyleSheet } from 'styled-components';
import { ColorModeScript } from '@chakra-ui/react';
import { SEO } from '@/components/app/SEO';
import {
  defaultPageTitle,
  defaultSiteTitle,
  defaultDescription
} from '../constants';

export default class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const sheet = new ServerStyleSheet();
    const originalRenderPage = ctx.renderPage;

    try {
      ctx.renderPage = () =>
        originalRenderPage({
          enhanceApp: (App) => (props) =>
            sheet.collectStyles(<App {...props} />)
        });

      const initialProps = await Document.getInitialProps(ctx);
      return {
        ...initialProps,
        styles: (
          <>
            {initialProps.styles}
            {sheet.getStyleElement()}
          </>
        )
      };
    } finally {
      sheet.seal();
    }
  }
  render() {
    return (
      <Html lang="en">
        <Head>
          <SEO
            title={defaultPageTitle}
            siteTitle={defaultSiteTitle}
            description={defaultDescription}
          />
          {/* Remove duplicate Google Fonts links and only keep what's needed */}
          <link href="https://fonts.googleapis.com/css2?family=Chakra+Petch:wght@400;700&display=swap" rel="stylesheet" />
        </Head>

        <body className="bg-black">
          <ColorModeScript />
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
