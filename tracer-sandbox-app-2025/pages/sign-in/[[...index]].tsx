import React from 'react';
import { StyledAppPageWrapper } from '@/components/common/Layout/StyledApp';
import { SignIn } from '@clerk/nextjs';
import useDisableScroll from 'hooks/useDisableScroll';

export default function SignInPage() {
  useDisableScroll();

  return (
    <StyledAppPageWrapper className="pl-0">
      <div className="flex justify-center items-center h-[70vh] w-full">
        <SignIn
          path="/sign-in"
          routing="path"
          signUpUrl="/sign-up"
          redirectUrl="/grafana"
          afterSignInUrl="https://sandbox.tracer.cloud/grafana"
        />
      </div>
    </StyledAppPageWrapper>
  );
}
