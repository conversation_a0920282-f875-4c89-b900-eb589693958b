import { ToastComponent } from '@/components/ui/Toast/Toast';
import { AppContextProvider } from '@/context/AppContext';
import { UIProvider } from '@/context/UIContext';
import { UserContextProvider } from '@/context/UserContext';
import { config } from '@fortawesome/fontawesome-svg-core';
import '@fortawesome/fontawesome-svg-core/styles.css';
import { HeadComponent } from 'components/app/HeadComponent';
import { withRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { Layout } from '../components/common/Layout';
import '../styles/global.css';
import '../styles/prism-xonokai.css';
// import { ChatProvider } from '@/context/ChatContext';
import { InvitesContextProvider } from '@/context/InvitesContext';
import { ServicesContextProvider } from '@/context/ServicesContext/ServicesContext';
import { TeamsContextProvider } from '@/context/TeamsContext/index';
import ToolDetailsProvider from '@/context/ToolDetailsContext';
import { ClerkProvider } from '@clerk/nextjs';
import LogRocket from 'logrocket';
import type { AppProps } from 'next/app';

// Tell Font Awesome to skip adding the CSS automatically since it's being imported above
config.autoAddCss = false;

function MyApp({ Component, pageProps, router }: AppProps) {
  const isAppPage =
    router.pathname.startsWith('/app') ||
    router.pathname.includes('/onboarding');
    
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    if (window.location.host.includes('localhost')) return;
    LogRocket.init('tracerbio/tracer-app-website');
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      document.documentElement.classList.add('dark');
    }
  }, []);

  // Prevent hydration errors by only rendering the full app after client-side hydration
  if (!mounted) {
    return (
      <div className="bg-black min-h-screen">
        <HeadComponent />
      </div>
    );
  }

  return (
    <div className="bg-black">
      <HeadComponent />
      <AppContextProvider>
        <ClerkProvider {...pageProps}>
          <UserAuthenticationWrapper
            Component={Component}
            pageProps={pageProps}
            isAppPage={isAppPage}
          />
        </ClerkProvider>
      </AppContextProvider>
    </div>
  );
}

function UserAuthenticationWrapper({ Component, pageProps, isAppPage }) {
  return (
    <UserContextProvider>
      {isAppPage ? (
          <ApplicationContextWrapper>
            <Layout>
              <Component {...pageProps} />
            </Layout>
          </ApplicationContextWrapper>
      ) : (
        <Layout>
          <Component {...pageProps} />
        </Layout>
      )}
    </UserContextProvider>
  );
}

function ApplicationContextWrapper({ children }) {
  return (
    <React.Fragment>
      <UIProvider>
        <UserContextProvider>
          <TeamsContextProvider>
            <InvitesContextProvider>
              <ServicesContextProvider>
                <ToolDetailsProvider>
                   {children}
                </ToolDetailsProvider>
                <ToastComponent />
              </ServicesContextProvider>
            </InvitesContextProvider>
          </TeamsContextProvider>
        </UserContextProvider>
      </UIProvider>
    </React.Fragment>
  );
}

export default withRouter(MyApp);
