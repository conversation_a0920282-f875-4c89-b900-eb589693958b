export type TCloudProvider = {
  name: string;
  imgSrc: string;
  id: string;
};

export const PROVIDER_AWS = 'AWS';
export const PROVIDER_GOOGLE_CLOUD = 'Google Cloud';
export const PROVIDER_AZURE_STORAGE = 'Azure';

export const CLOUD_STORAGE_PROVIDERS: Record<string, TCloudProvider> = {
  [PROVIDER_AWS]: {
    name: 'AWS',
    imgSrc: '/assets/aws-s3-logo.webp',
    id: PROVIDER_AWS
  },
  [PROVIDER_GOOGLE_CLOUD]: {
    name: 'Google Cloud',
    imgSrc: '/assets/google-cloud-storage-logo.svg',
    id: PROVIDER_GOOGLE_CLOUD
  },
  [PROVIDER_AZURE_STORAGE]: {
    name: 'Azure',
    imgSrc: '/assets/ms-azure-logo.png',
    id: PROVIDER_AZURE_STORAGE
  }
};
