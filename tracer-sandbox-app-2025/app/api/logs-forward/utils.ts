import { Pool } from 'pg'

// Database connection configurations
export const devPool = new Pool({
  connectionString: process.env.DATABASE_URL_DEV,
  ssl: { rejectUnauthorized: false },
});

export const prodPool = new Pool({
  connectionString: process.env.DATABASE_URL_PROD,
  ssl: { rejectUnauthorized: false },
});

// Define allowed columns as a Set for O(1) lookup
const allowedColumns = new Set([
  'timestamp', 'body', 'severity_text', 'severity_number',
  'trace_id', 'span_id',
  'source_type', 'instrumentation_version', 'instrumentation_type',
  'environment', 'pipeline_type', 'user_operator', 'organization_id', 'department',
  'run_id', 'run_name', 'pipeline_name',
  'job_id', 'parent_job_id', 'child_job_ids', 'workflow_engine',
  'ec2_cost_per_hour', 'cpu_usage', 'mem_used', 'processed_dataset',
  'process_status', 'event_type', 'process_type',
  'attributes', 'resource_attributes', 'tags'
]);

// Export columns as an array for other uses
export const columns = Array.from(allowedColumns);

// Validate that all columns in the data are allowed
function validateColumns(data: Record<string, any>): string[] {
  const validColumns = Object.keys(data).filter(col => allowedColumns.has(col));
  if (validColumns.length === 0) {
    throw new Error('No valid columns found in data');
  }
  return validColumns;
}

export async function handleLogsInsertion(pool: Pool, items: any[]) {
  const client = await pool.connect()
  try {
    console.log("items: " + JSON.stringify(items))

    for (const item of items) {
      // Validate and get only allowed columns
      const validColumns = validateColumns(item);
      
      // Create values array only for valid columns
      const values = validColumns.map(col => item[col] ?? null);
      
      // Create placeholders for the valid columns
      const placeholders = validColumns.map((_, i) => `$${i + 1}`).join(', ');
      
      // Use parameterized query with validated column names
      const query = {
        text: `INSERT INTO batch_jobs_logs (${validColumns.join(', ')}) VALUES (${placeholders})`,
        values: values
      };
      
      await client.query(query);
    }
    return { success: true, data: items }
  } finally {
    client.release()
  }
} 