import { NextResponse } from 'next/server'
import { prodPool, handleLogsInsertion } from '../utils'

export async function POST(request: Request) {
  try {
    const payload = await request.json()
    const result = await handleLogsInsertion(prodPool, payload.events)
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({ success: true })
} 