<!DOCTYPE html>
<html lang="en">
  <head>
    <link
      rel="stylesheet"
      type="text/css"
      href="https://fonts.googleapis.com/css?family=PT+Serif|Open+Sans:300,400,600,700,800"
    />
    <!-- Roboto font -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
    />

    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="%PUBLIC_URL%/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="%PUBLIC_URL%/favicon-32x32.png"
      sizes="32x32"
      color="#000000"
    />
    <link
      rel="icon"
      type="image/png"
      href="%PUBLIC_URL%/favicon-16x16.png"
      sizes="16x16"
      color="#000000"
    />
    <link
      rel="mask-icon"
      href="%PUBLIC_URL%/safari-pinned-tab.svg"
      color="#5bbad5"
    />
    <meta name="description" content="Error Monitoring For Biology | Tracer" />
    <meta name="theme-color" content="#ffffff" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Tracer | Error Monitoring</title>
    <!-- REMOVE THIS STUFF STYLES -->

    <script src="THREE.MeshLine.js"></script>
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
